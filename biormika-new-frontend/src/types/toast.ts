// Toast notification types
export interface ToastOptions {
  id?: string | number
  duration?: number
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
  description?: string
  action?: {
    label: string
    onClick: () => void
  }
  cancel?: {
    label: string
    onClick?: () => void
  }
  onDismiss?: (id: string | number) => void
  onAutoClose?: (id: string | number) => void
}

export interface ToastConfig {
  position?: ToastOptions['position']
  duration?: number
  richColors?: boolean
  theme?: 'light' | 'dark' | 'system'
  closeButton?: boolean
  pauseWhenPageIsHidden?: boolean
  visibleToasts?: number
}

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading' | 'default'