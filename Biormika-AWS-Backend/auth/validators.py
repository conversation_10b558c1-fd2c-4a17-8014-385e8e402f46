import re
from typing import <PERSON><PERSON>

def validate_email(email: str) -> <PERSON><PERSON>[bool, str]:
    if not email:
        return False, "Email is required"
    
    email = email.strip().lower()
    email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    
    if not email_pattern.match(email):
        return False, "Invalid email format"
    
    if len(email) > 254:
        return False, "Email address too long"
    
    return True, email

def validate_name(name: str) -> Tuple[bool, str]:
    if not name:
        return False, "Name is required"
    
    name = name.strip()
    
    if len(name) < 2:
        return False, "Name must be at least 2 characters long"
    
    if len(name) > 100:
        return False, "Name must be less than 100 characters"
    
    if not re.match(r'^[a-zA-Z\s\'-]+$', name):
        return False, "Name can only contain letters, spaces, hyphens, and apostrophes"
    
    return True, name

def sanitize_input(input_string: str) -> str:
    if not input_string:
        return ""
    
    return input_string.strip()[:1000]