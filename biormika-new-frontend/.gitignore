# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
*.mdc
TASKS.md
.cursor/
CLAUDE.md
.claude/
tasks/  
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
deploy.js
.env
.env.*
!.env.example
*.env
*.env.*
!.env.example

# OS generated files
.DS_Store
Thumbs.db

# Build output
build/
out/
coverage/
coverage-*

# Misc
*.tgz
*.zip
*.tar
*.tar.gz
*.7z
*.bak
*.tmp
*.temp
*.orig
*.rej

# IDEs and editors
*.iml
*.sublime-workspace
*.sublime-project

# System files
ehthumbs.db
Icon?
Desktop.ini

# Test output
test-output/
junit-report.xml


