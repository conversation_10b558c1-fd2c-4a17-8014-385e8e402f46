import type { ReactNode } from 'react'
import { useRouteError } from 'react-router-dom'

interface RouteErrorBoundaryProps {
  children: ReactNode
}

function RouteErrorFallback() {
  const error = useRouteError() as Error

  return (
    <div>
      <h1>Route Error</h1>
      <p>{error?.message || 'An error occurred while loading this page'}</p>
    </div>
  )
}

export function RouteErrorBoundary({ children }: RouteErrorBoundaryProps) {
  return children
}

export { RouteErrorFallback as RouteErrorElement }