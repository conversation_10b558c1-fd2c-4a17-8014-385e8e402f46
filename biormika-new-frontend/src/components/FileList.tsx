import { Trash2, CheckCircle, AlertCircle, Clock, Loader, Info, Settings, Plus, RefreshCw, Download } from 'lucide-react'
import { useAppSelector } from '../store'
import { useFileUpload } from '../hooks/useFileUpload'
import { FileStatus } from '../types/files'
import { brandColors } from '../constants/colors'
import { filesService } from '../services/files.service'

interface FileListProps {
  showActions?: boolean
  onContinue?: () => void
  onReUpload?: () => void
  onAddMoreFiles?: () => void
  onConfigure?: (fileId: string) => void
  hideHeader?: boolean
}

export function FileList({ showActions = true, onContinue, onReUpload, onAddMoreFiles, onConfigure, hideHeader = false }: FileListProps) {
  const { files, deleteFile, cancelUpload, retryUpload } = useFileUpload()
  const { fileSpecificSettings } = useAppSelector(state => state.analysisSettings)

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (status: FileStatus) => {
    switch (status) {
      case FileStatus.VALID:
      case FileStatus.COMPLETED:
        return <CheckCircle className="w-4 h-4" style={{ color: brandColors.success }} />
      case FileStatus.ERROR:
        return <AlertCircle className="w-4 h-4" style={{ color: brandColors.error }} />
      case FileStatus.UPLOADING:
      case FileStatus.VALIDATING:
        return <Loader className="w-4 h-4 animate-spin" style={{ color: brandColors.primary }} />
      default:
        return <Clock className="w-4 h-4" style={{ color: brandColors.text.muted }} />
    }
  }

  const getStatusText = (status: FileStatus) => {
    switch (status) {
      case FileStatus.VALID:
        return 'Valid EDF'
      case FileStatus.COMPLETED:
        return 'Completed'
      case FileStatus.ERROR:
        return 'Error'
      case FileStatus.UPLOADING:
        return 'Uploading'
      case FileStatus.VALIDATING:
        return 'Validating'
      case FileStatus.PENDING:
        return 'Pending'
      default:
        return 'Unknown'
    }
  }

  const handleRemoveFile = async (fileId: string) => {
    const file = files.find(f => f.id === fileId)
    if (file?.status === 'UPLOADING') {
      cancelUpload(fileId)
    } else {
      await deleteFile(fileId, file?.serverFileId)
    }
  }

  const handleDownloadFile = async (fileId: string, fileName: string) => {
    try {
      await filesService.downloadFile(fileId, fileName)
    } catch (error) {
      console.error('Download error:', error)
    }
  }

  if (files.length === 0) {
    return null
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Header Section */}
      {!hideHeader && (
        <div className="text-center mb-8">
          <h1 className="text-2xl font-semibold mb-4" style={{ color: brandColors.text.primary }}>
            Review Uploaded EDF File
          </h1>
          <p className="text-base max-w-2xl mx-auto" style={{ color: brandColors.text.secondary }}>
            Please confirm that the file below is correct before continuing. Ensure it is the intended 
            EDF file, redacted and ready for analysis. If not, you can return to re-upload another file.
          </p>
        </div>
      )}

      {/* Custom Table */}
      <div className="bg-white rounded-lg overflow-hidden mb-6">
        {/* Table Header */}
        <div className={`grid gap-4 px-6 py-4 border-b ${showActions ? 'grid-cols-4' : 'grid-cols-3'}`} style={{ borderColor: brandColors.border.light, backgroundColor: brandColors.background.light }}>
          <div className="font-medium" style={{ color: brandColors.text.secondary }}>Filename</div>
          <div className="font-medium" style={{ color: brandColors.text.secondary }}>Size</div>
          <div className="font-medium" style={{ color: brandColors.text.secondary }}>Status</div>
          {showActions && <div className="font-medium" style={{ color: brandColors.text.secondary }}>Actions</div>}
        </div>
        
        {/* Table Rows */}
        {files.map((file) => (
          <div key={file.id} className={`grid gap-4 px-6 py-4 items-center border-b last:border-b-0 ${showActions ? 'grid-cols-4' : 'grid-cols-3'}`} style={{ borderColor: brandColors.border.light }}>
            {/* Filename Column */}
            <div className="flex items-center gap-3">
              <div 
                className="flex-shrink-0 px-2 py-1 rounded text-xs font-medium"
                style={{ 
                  backgroundColor: brandColors.border.light,
                  color: brandColors.text.secondary 
                }}
              >
                EDF
              </div>
              <span 
                className="font-medium truncate" 
                style={{ color: brandColors.text.primary }}
                title={file.name}
              >
                {file.name}
              </span>
            </div>
            
            {/* Size Column */}
            <div style={{ color: brandColors.text.secondary }}>
              {formatFileSize(file.size)}
            </div>
            
            {/* Status Column */}
            <div className="flex items-center gap-2">
              {getStatusIcon(file.status)}
              <span 
                className="font-medium"
                style={{ 
                  color: file.status === FileStatus.VALID || file.status === FileStatus.COMPLETED 
                    ? brandColors.success
                    : file.status === FileStatus.ERROR 
                    ? brandColors.error 
                    : brandColors.text.secondary
                }}
              >
                {getStatusText(file.status)}
              </span>
            </div>
            
            {/* Actions Column */}
            {showActions && (
              <div className="flex items-center gap-2">
                {file.status === FileStatus.ERROR && (
                  <button
                    onClick={() => retryUpload(file.id)}
                    className="p-1 hover:bg-blue-50 rounded"
                    style={{ color: brandColors.primary }}
                    title="Retry upload"
                  >
                    <RefreshCw className="w-4 h-4" />
                  </button>
                )}
                {file.status === FileStatus.VALID && file.serverFileId && (
                  <button
                    onClick={() => handleDownloadFile(file.serverFileId!, file.name)}
                    className="p-1 hover:bg-blue-50 rounded"
                    style={{ color: brandColors.primary }}
                    title="Download file"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                )}
                {onConfigure && file.status === FileStatus.VALID && (
                  <button
                    onClick={() => onConfigure(file.id)}
                    className="p-1 hover:bg-blue-50 rounded relative"
                    style={{ color: brandColors.primary }}
                    title={fileSpecificSettings[file.id] 
                      ? "Configure settings for this file (has custom settings)" 
                      : "Configure settings for this file"
                    }
                  >
                    <Settings className="w-4 h-4" />
                    {fileSpecificSettings[file.id] && (
                      <div 
                        className="absolute -top-1 -right-1 w-2 h-2 rounded-full"
                        style={{ backgroundColor: brandColors.primary }}
                      />
                    )}
                  </button>
                )}
                <button
                  onClick={() => handleRemoveFile(file.id)}
                  className="p-1 hover:bg-red-50 rounded"
                  style={{ color: brandColors.error }}
                  title={file.status === FileStatus.UPLOADING ? "Cancel upload" : "Remove file"}
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      {(onContinue || onReUpload || onAddMoreFiles) && (
        <div className="flex justify-between items-center mb-6">
          <div className="flex gap-3">
            {onReUpload && (
              <button
                onClick={onReUpload}
                className="px-6 py-3 rounded-lg font-medium border transition-colors"
                style={{ 
                  borderColor: brandColors.border.medium,
                  color: brandColors.text.secondary,
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = brandColors.background.light
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                Re-upload
              </button>
            )}
            {onAddMoreFiles && (
              <button
                onClick={onAddMoreFiles}
                className="flex items-center gap-2 px-6 py-3 rounded-lg font-medium border transition-colors"
                style={{ 
                  borderColor: brandColors.primary,
                  color: brandColors.primary,
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = `${brandColors.primary}10`
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <Plus className="w-4 h-4" />
                Add More Files
              </button>
            )}
          </div>
          {onContinue && (
            <button
              onClick={onContinue}
              className="px-6 py-3 rounded-lg font-medium text-white transition-colors"
              style={{ backgroundColor: brandColors.actionButton }}
              disabled={files.some(file => file.status === FileStatus.ERROR || file.status === FileStatus.PENDING || file.status === FileStatus.UPLOADING || file.status === FileStatus.VALIDATING)}
              onMouseEnter={(e) => {
                if (!e.currentTarget.disabled) {
                  e.currentTarget.style.backgroundColor = brandColors.secondary
                }
              }}
              onMouseLeave={(e) => {
                if (!e.currentTarget.disabled) {
                  e.currentTarget.style.backgroundColor = brandColors.actionButton
                }
              }}
            >
              Continue
            </button>
          )}
        </div>
      )}
      
      {/* Privacy Notice */}
      <div className="flex items-center justify-center gap-2 text-sm" style={{ color: brandColors.text.muted }}>
        <Info className="w-4 h-4" />
        <span>No patient data is retained or shared.</span>
      </div>
    </div>
  )
}