import type { ReactNode } from 'react'
import { UserProfileHeader } from './UserProfileHeader'
import { PageFooter } from './PageFooter'
import { brandColors } from '../constants/colors'

interface PageLayoutProps {
  children: ReactNode
  showFooter?: boolean
  className?: string
}

export function PageLayout({ children, showFooter = false, className = '' }: PageLayoutProps) {
  return (
    <div 
      className={`min-h-screen flex flex-col ${className}`}
      style={{ backgroundColor: brandColors.background.light }}
    >
      <UserProfileHeader />
      
      <main className="flex-1 flex flex-col items-center justify-center px-6 py-12">
        {children}
      </main>

      {showFooter && <PageFooter />}
    </div>
  )
}