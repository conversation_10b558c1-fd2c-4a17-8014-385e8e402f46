{"permissions": {"allow": ["mcp__filesystem__list_directory", "mcp__filesystem__directory_tree", "mcp__filesystem__read_file", "mcp__filesystem__search_files", "mcp__sequentialthinking__sequentialthinking", "mcp__filesystem__list_allowed_directories", "mcp__filesystem__read_multiple_files", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run deploy:dev:*)", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(chmod:*)", "Bash(ls:*)", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(docker:*)", "Bash(/dev/null)", "Bash(aws cloudformation wait:*)", "Bash(grep:*)", "<PERSON><PERSON>(true)", "Bash(aws cloudformation:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_file_upload", "mcp__playwright__browser_click", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_wait_for", "mcp__puppeteer__puppeteer_navigate", "mcp__perplexity-ask__perplexity_ask"], "deny": []}}