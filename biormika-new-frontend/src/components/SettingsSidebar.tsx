import { useEffect } from 'react'
import { Form, Select, Checkbox, Button } from 'antd'
import { X } from 'lucide-react'
import { useAppDispatch, useAppSelector } from '../store'
import { setFileSpecificSettings, removeFileSpecificSettings, defaultAnalysisSettings } from '../store/slices/analysisSettingsSlice'
import { brandColors } from '../constants/colors'
import type { AnalysisSettings } from '../types/analysis'

interface SettingsSidebarProps {
  isOpen: boolean
  onClose: () => void
  fileId: string
  fileName: string
}

export function SettingsSidebar({ isOpen, onClose, fileId, fileName }: SettingsSidebarProps) {
  const [form] = Form.useForm()
  const dispatch = useAppDispatch()
  const { globalSettings, fileSpecificSettings } = useAppSelector(state => state.analysisSettings)

  useEffect(() => {
    if (isOpen) {
      const currentFileSettings = fileSpecificSettings[fileId]
      const settingsToUse = currentFileSettings || globalSettings || defaultAnalysisSettings
      form.setFieldsValue(settingsToUse)
    }
  }, [isOpen, fileId, fileSpecificSettings, globalSettings, form])

  const handleSubmit = async (values: AnalysisSettings) => {
    dispatch(setFileSpecificSettings({ settings: values, fileId }))
    onClose()
  }

  const handleReset = () => {
    const settingsToUse = globalSettings || defaultAnalysisSettings
    form.setFieldsValue(settingsToUse)
    // Remove file-specific settings from Redux to restore default behavior
    dispatch(removeFileSpecificSettings(fileId))
  }

  if (!isOpen) return null

  const sectionHeaderStyle = {
    backgroundColor: brandColors.background.light,
    color: brandColors.text.secondary,
    padding: '8px 12px',
    fontSize: '12px',
    fontWeight: '500',
    borderBottom: `1px solid ${brandColors.border.light}`,
    margin: 0,
  }

  const containerStyle = {
    backgroundColor: 'white',
    borderRadius: '6px',
    border: `1px solid ${brandColors.border.light}`,
    marginBottom: '16px',
  }

  return (
    <>
      {/* Invisible backdrop for click-to-close */}
      <div 
        className="fixed inset-0 z-[9998]"
        onClick={onClose}
        style={{ backgroundColor: 'transparent' }}
      />
      
      {/* Sidebar */}
      <div 
        className="fixed right-0 top-0 h-full w-96 shadow-2xl z-[9999] overflow-y-auto"
        style={{ 
          backgroundColor: 'white',
          border: `1px solid ${brandColors.border.light}`,
          borderRight: 'none',
          boxShadow: '-4px 0 15px rgba(0, 0, 0, 0.15)'
        }}
      >
        {/* Header */}
        <div 
          className="flex items-center justify-between p-4 border-b" 
          style={{ 
            borderColor: brandColors.border.light,
            backgroundColor: 'white'
          }}
        >
          <div>
            <h2 className="text-lg font-semibold" style={{ color: brandColors.text.primary }}>
              File Settings
            </h2>
            <p 
              className="text-sm truncate" 
              style={{ color: brandColors.text.secondary }}
              title={fileName}
            >
              {fileName}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            style={{ color: brandColors.text.secondary }}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4" style={{ backgroundColor: 'white' }}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            className="space-y-4"
          >
            {/* Threshold Settings */}
            <div style={containerStyle}>
              <h3 style={sectionHeaderStyle}>Threshold Settings</h3>
              <div className="p-4 space-y-3">
                <div>
                  <label className="block text-xs font-medium mb-1" style={{ color: brandColors.text.secondary }}>
                    Amplitude 1
                  </label>
                  <Form.Item name={['thresholdSettings', 'amplitude1']} className="mb-0">
                    <Select size="small" style={{ height: '32px' }}>
                      {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                        <Select.Option key={num} value={num}>{num}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>

                <div>
                  <label className="block text-xs font-medium mb-1" style={{ color: brandColors.text.secondary }}>
                    Amplitude 2
                  </label>
                  <Form.Item name={['thresholdSettings', 'amplitude2']} className="mb-0">
                    <Select size="small" style={{ height: '32px' }}>
                      {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                        <Select.Option key={num} value={num}>{num}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>

                <div>
                  <label className="block text-xs font-medium mb-1" style={{ color: brandColors.text.secondary }}>
                    Peaks 1
                  </label>
                  <Form.Item name={['thresholdSettings', 'peaks1']} className="mb-0">
                    <Select size="small" style={{ height: '32px' }}>
                      {[3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                        <Select.Option key={num} value={num}>{num}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>

                <div>
                  <label className="block text-xs font-medium mb-1" style={{ color: brandColors.text.secondary }}>
                    Peaks 2
                  </label>
                  <Form.Item name={['thresholdSettings', 'peaks2']} className="mb-0">
                    <Select size="small" style={{ height: '32px' }}>
                      {[3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                        <Select.Option key={num} value={num}>{num}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>

                <div>
                  <label className="block text-xs font-medium mb-1" style={{ color: brandColors.text.secondary }}>
                    Duration (ms)
                  </label>
                  <Form.Item name={['thresholdSettings', 'duration']} className="mb-0">
                    <Select size="small" style={{ height: '32px' }}>
                      {[5, 10, 15, 20, 25, 30].map(num => (
                        <Select.Option key={num} value={num}>{num} ms</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>
              </div>
            </div>

            {/* Synchronization Settings */}
            <div style={containerStyle}>
              <h3 style={sectionHeaderStyle}>Synchronization Settings</h3>
              <div className="p-4 space-y-3">
                <div>
                  <label className="block text-xs font-medium mb-1" style={{ color: brandColors.text.secondary }}>
                    Temporal Sync (ms)
                  </label>
                  <Form.Item name={['synchronizationSettings', 'temporalSync']} className="mb-0">
                    <Select size="small" style={{ height: '32px' }}>
                      {[5, 10, 15, 20, 25, 30].map(num => (
                        <Select.Option key={num} value={num}>{num} ms</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>

                <div>
                  <label className="block text-xs font-medium mb-1" style={{ color: brandColors.text.secondary }}>
                    Spatial Sync (ms)
                  </label>
                  <Form.Item name={['synchronizationSettings', 'spatialSync']} className="mb-0">
                    <Select size="small" style={{ height: '32px' }}>
                      {[5, 10, 15, 20, 25, 30].map(num => (
                        <Select.Option key={num} value={num}>{num} ms</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>
              </div>
            </div>

            {/* Montage Selection */}
            <div style={containerStyle}>
              <h3 style={sectionHeaderStyle}>Montage Selection</h3>
              <div className="p-4 space-y-2">
                <Form.Item name={['montageSelection', 'bipolar']} valuePropName="checked" className="mb-0">
                  <Checkbox>Bipolar</Checkbox>
                </Form.Item>
                <Form.Item name={['montageSelection', 'average']} valuePropName="checked" className="mb-0">
                  <Checkbox>Average</Checkbox>
                </Form.Item>
                <Form.Item name={['montageSelection', 'referential']} valuePropName="checked" className="mb-0">
                  <Checkbox>Referential</Checkbox>
                </Form.Item>
              </div>
            </div>

            {/* Frequency Filter Settings */}
            <div style={containerStyle}>
              <h3 style={sectionHeaderStyle}>Frequency Filter Settings</h3>
              <div className="p-4 space-y-3">
                <div>
                  <label className="block text-xs font-medium mb-1" style={{ color: brandColors.text.secondary }}>
                    Low Cutoff Filter (Hz)
                  </label>
                  <Form.Item name={['frequencyFilterSettings', 'lowCutoffFilter']} className="mb-0">
                    <Select size="small" style={{ height: '32px' }}>
                      {[10, 20, 30, 40, 50, 60, 70, 80, 90, 100].map(num => (
                        <Select.Option key={num} value={num}>{num}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>

                <div>
                  <label className="block text-xs font-medium mb-1" style={{ color: brandColors.text.secondary }}>
                    High Cutoff Filter (Hz)
                  </label>
                  <Form.Item name={['frequencyFilterSettings', 'highCutoffFilter']} className="mb-0">
                    <Select size="small" style={{ height: '32px' }}>
                      {[200, 300, 400, 500, 600, 700, 800, 900, 1000].map(num => (
                        <Select.Option key={num} value={num}>{num}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2 pt-4">
              <Button
                type="default"
                onClick={handleReset}
                size="small"
                className="flex-1"
              >
                Reset to Global
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                size="small"
                className="flex-1"
                style={{
                  backgroundColor: brandColors.actionButton,
                  borderColor: brandColors.actionButton,
                }}
              >
                Save Settings
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </>
  )
}