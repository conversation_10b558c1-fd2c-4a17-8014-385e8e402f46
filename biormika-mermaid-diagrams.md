# Biormika System Architecture and Flow Diagrams

This document contains comprehensive Mermaid diagrams visualizing the various flows and architecture of the Biormika EEG/HFO Analysis Platform.

## 1. Authentication Flow Diagram

This diagram shows the complete authentication flow including login, signup, social authentication, and token refresh mechanisms.

```mermaid
flowchart TB
    Start([User Access]) --> CheckAuth{Is Authenticated?}
    
    CheckAuth -->|No| LoginPage[Login Page]
    CheckAuth -->|Yes| Dashboard[Dashboard]
    
    LoginPage --> AuthMethod{Choose Auth Method}
    
    AuthMethod -->|Email/Password| EmailLogin[Enter Credentials]
    AuthMethod -->|Social| SocialAuth{Select Provider}
    AuthMethod -->|New User| SignupFlow[Signup Page]
    
    SocialAuth -->|Google| GoogleOAuth[Google OAuth Flow]
    SocialAuth -->|Facebook| FacebookOAuth[Facebook OAuth Flow]
    
    EmailLogin --> ValidateCreds[Validate Credentials]
    ValidateCreds -->|Valid| CognitoAuth[AWS Cognito Authentication]
    ValidateCreds -->|Invalid| ShowError[Show Error Message]
    
    GoogleOAuth --> OAuthCallback[OAuth Callback]
    FacebookOAuth --> OAuthCallback
    OAuthCallback --> CreateOrFindUser[Create/Find Cognito User]
    
    SignupFlow --> EnterDetails[Enter User Details]
    EnterDetails --> CreateUser[Create Cognito User]
    CreateUser --> SendVerification[Send Email Verification]
    SendVerification --> VerifyEmail[User Verifies Email]
    VerifyEmail --> AutoLogin[Auto Login After Verification]
    
    CognitoAuth --> GenerateTokens[Generate JWT Tokens]
    CreateOrFindUser --> GenerateTokens
    AutoLogin --> GenerateTokens
    
    GenerateTokens --> StoreTokens[Store in Redux + LocalStorage]
    StoreTokens --> SetAxiosHeaders[Set Authorization Headers]
    SetAxiosHeaders --> Dashboard
    
    Dashboard --> MakeAPICall[Make API Request]
    MakeAPICall --> CheckTokenExpiry{Token Expired?}
    
    CheckTokenExpiry -->|No| ProcessRequest[Process Request]
    CheckTokenExpiry -->|Yes| RefreshToken[Refresh Token Flow]
    
    RefreshToken --> QueueRequests[Queue Pending Requests]
    QueueRequests --> CallRefreshEndpoint[Call /auth/refresh]
    CallRefreshEndpoint -->|Success| UpdateTokens[Update Tokens]
    CallRefreshEndpoint -->|Fail| RedirectLogin[Redirect to Login]
    
    UpdateTokens --> ReplayQueue[Replay Queued Requests]
    ReplayQueue --> ProcessRequest
    
    RedirectLogin --> LoginPage
    ShowError --> LoginPage
    
    style Dashboard fill:#90EE90
    style LoginPage fill:#FFB6C1
    style GenerateTokens fill:#87CEEB
    style RefreshToken fill:#DDA0DD
```

## 2. File Upload and Processing Flow

This diagram illustrates the complete file upload, validation, and analysis pipeline from frontend to backend processing.

```mermaid
flowchart TB
    Start([User Selects EDF File]) --> ValidateClient{Client-side Validation}
    
    ValidateClient -->|Invalid| ShowError[Show Error:<br/>- File type must be .edf<br/>- Max size: 100MB]
    ValidateClient -->|Valid| InitUpload[Call /files/upload/init]
    
    ShowError --> Start
    
    InitUpload --> CreateMetadata[Backend Creates:<br/>- File ID<br/>- DynamoDB Entry<br/>- Presigned S3 URL]
    
    CreateMetadata --> ReturnUploadUrl[Return Upload URL & Fields]
    ReturnUploadUrl --> DirectS3Upload[Direct Upload to S3<br/>with Progress Tracking]
    
    DirectS3Upload --> UploadStatus{Upload Status}
    UploadStatus -->|Failed| RetryUpload[Retry with Resume]
    UploadStatus -->|Cancelled| Cleanup[Clean Up Resources]
    UploadStatus -->|Success| ValidateFile[Call /files/validate/{fileId}]
    
    RetryUpload --> DirectS3Upload
    
    ValidateFile --> BackendValidation[Backend Validation:<br/>- Download from S3<br/>- Parse EDF Header<br/>- Extract Metadata]
    
    BackendValidation --> ValidationResult{Validation Result}
    ValidationResult -->|Invalid| MarkInvalid[Mark File as INVALID<br/>Return Error Details]
    ValidationResult -->|Valid| ExtractInfo[Extract & Store:<br/>- Channel Count<br/>- Sampling Rate<br/>- Duration<br/>- Channel Labels]
    
    ExtractInfo --> UpdateStatus[Update File Status: VALID]
    UpdateStatus --> ShowFileReady[Show File Ready<br/>Enable Analysis Button]
    
    ShowFileReady --> UserStartsAnalysis{User Starts Analysis}
    UserStartsAnalysis -->|Configure| SetParameters[Set Analysis Parameters:<br/>- Thresholds<br/>- Frequency Filters<br/>- Montage Type]
    
    SetParameters --> StartAnalysis[Call /analysis/start]
    StartAnalysis --> CreateAnalysisRecord[Create Analysis Record<br/>Generate Analysis ID]
    
    CreateAnalysisRecord --> StartStepFunction[Start AWS Step Function]
    StartStepFunction --> OpenWebSocket[Open WebSocket Connection<br/>Subscribe to Analysis ID]
    
    StartStepFunction --> SF1[Step 1: Read EDF File<br/>Progress: 10-25%]
    SF1 --> SF2[Step 2: Detect HFOs<br/>Progress: 40-70%]
    SF2 --> SF3[Step 3: Generate Visualizations<br/>Progress: 85-95%]
    SF3 --> SF4[Step 4: Process Results<br/>Progress: 100%]
    
    SF1 --> WS1[WebSocket: Reading file...]
    SF2 --> WS2[WebSocket: Detecting HFOs...]
    SF3 --> WS3[WebSocket: Creating visualizations...]
    SF4 --> WS4[WebSocket: Generating report...]
    
    SF4 --> CompleteAnalysis[Analysis Complete:<br/>- Results in S3<br/>- PDF Report Ready<br/>- Update DynamoDB]
    
    CompleteAnalysis --> NotifyComplete[WebSocket: Analysis Complete<br/>with Download URLs]
    NotifyComplete --> ShowResults[Display Results:<br/>- HFO Summary<br/>- Download Button<br/>- View Details]
    
    style ShowFileReady fill:#90EE90
    style ShowError fill:#FFB6C1
    style CompleteAnalysis fill:#87CEEB
    style StartStepFunction fill:#DDA0DD
```

## 3. System Architecture Diagram

This diagram provides a high-level view of all components and their interactions in the Biormika platform.

```mermaid
graph TB
    subgraph "Frontend - React SPA"
        UI[React Components]
        Redux[Redux Store]
        Router[React Router]
        Services[Service Layer]
        WSClient[WebSocket Client]
        
        UI <--> Redux
        UI <--> Router
        Redux <--> Services
        Services <--> WSClient
    end
    
    subgraph "API Gateway"
        REST[REST API<br/>Endpoints]
        WS[WebSocket API]
    end
    
    subgraph "Authentication"
        Cognito[AWS Cognito]
        JWT[JWT Tokens]
    end
    
    subgraph "Compute Layer"
        Lambda[Lambda Functions]
        StepFunctions[Step Functions<br/>Orchestration]
    end
    
    subgraph "Storage Layer"
        S3[S3 Buckets]
        DynamoDB[DynamoDB Tables]
    end
    
    subgraph "Processing Pipeline"
        EDF[EDF Reader]
        HFO[HFO Detector]
        VIZ[Visualization<br/>Generator]
        PDF[PDF Report<br/>Generator]
        
        EDF --> HFO
        HFO --> VIZ
        VIZ --> PDF
    end
    
    subgraph "Monitoring"
        CloudWatch[CloudWatch Logs]
        Metrics[CloudWatch Metrics]
    end
    
    UI -->|HTTPS| REST
    WSClient -->|WSS| WS
    
    REST --> Cognito
    REST --> Lambda
    WS --> Lambda
    
    Lambda --> StepFunctions
    Lambda --> S3
    Lambda --> DynamoDB
    Lambda --> CloudWatch
    
    StepFunctions --> EDF
    
    EDF --> S3
    HFO --> S3
    VIZ --> S3
    PDF --> S3
    
    Cognito --> JWT
    JWT --> REST
    
    style Frontend fill:#E6F3FF
    style "API Gateway" fill:#FFE6E6
    style Authentication fill:#E6FFE6
    style "Compute Layer" fill:#FFFFE6
    style "Storage Layer" fill:#F0E6FF
    style "Processing Pipeline" fill:#E6FFF0
    style Monitoring fill:#FFE6F0
```

## 4. HFO Detection Algorithm Flow

This diagram details the HFO detection algorithm steps and decision points based on the Threshold Scheme 10 implementation.

```mermaid
flowchart TB
    Start([Start HFO Detection]) --> LoadData[Load EDF Data<br/>from S3]
    
    LoadData --> ApplyMontage{Select Montage}
    ApplyMontage -->|Bipolar| BipolarMontage[Apply Bipolar Montage]
    ApplyMontage -->|Average| AverageMontage[Apply Average Reference]
    ApplyMontage -->|Referential| ReferentialMontage[Keep Original Reference]
    
    BipolarMontage --> FilterSignal
    AverageMontage --> FilterSignal
    ReferentialMontage --> FilterSignal
    
    FilterSignal[Apply Filters:<br/>1. Notch Filter (60Hz, Q=30)<br/>2. Bandpass Filter]
    
    FilterSignal --> FreqRange{Frequency Range}
    FreqRange -->|Ripples| RippleFilter[80-250 Hz<br/>Butterworth 6th order]
    FreqRange -->|Fast Ripples| FastRippleFilter[250-500 Hz<br/>Butterworth 6th order]
    
    RippleFilter --> DetectBlanks
    FastRippleFilter --> DetectBlanks
    
    DetectBlanks[Detect & Remove<br/>Data Discontinuities]
    DetectBlanks --> CalcHilbert[Calculate Hilbert Transform<br/>for Signal Envelope]
    
    CalcHilbert --> CalcBaseline[Calculate Baseline:<br/>Mean & STD of Envelope]
    
    CalcBaseline --> SetThreshold[Set Detection Threshold:<br/>baseline + (multiplier × STD)<br/>Default multiplier: 5]
    
    SetThreshold --> ScanSignal[Scan Signal for<br/>Above-Threshold Segments]
    
    ScanSignal --> CheckDuration{Duration ≥ 10ms?}
    CheckDuration -->|No| NextSegment[Move to Next Segment]
    CheckDuration -->|Yes| CountPeaks[Count Peaks in Segment]
    
    NextSegment --> ScanSignal
    
    CountPeaks --> ValidatePeaks{Validate Peaks:<br/>≥6 peaks > amplitude1?<br/>≥3 peaks > amplitude2?}
    
    ValidatePeaks -->|No| NextSegment
    ValidatePeaks -->|Yes| EstimateFreq[Estimate Frequency<br/>using FFT]
    
    EstimateFreq --> CheckFreq{Frequency ≥ 70Hz?}
    CheckFreq -->|No| NextSegment
    CheckFreq -->|Yes| CalcPowerRatio[Calculate Power Ratio:<br/>HFO band vs Conventional]
    
    CalcPowerRatio --> ValidateRatio{Power Ratio Valid?}
    ValidateRatio -->|No| NextSegment
    ValidateRatio -->|Yes| MarkHFO[Mark as Valid HFO<br/>Store Event Details]
    
    MarkHFO --> MoreData{More Data?}
    MoreData -->|Yes| ScanSignal
    MoreData -->|No| SyncAnalysis[Analyze Synchronization<br/>Across Channels]
    
    SyncAnalysis --> TemporalSync[Find Temporal Synchrony:<br/>Events within 10ms window]
    TemporalSync --> SpatialSync[Find Spatial Synchrony:<br/>Events in adjacent channels]
    
    SpatialSync --> AggregateResults[Aggregate Results:<br/>- Total HFO count<br/>- HFO rate per channel<br/>- Synchronized events<br/>- Duration statistics]
    
    AggregateResults --> SaveResults[Save Results to S3<br/>as JSON]
    SaveResults --> End([End Detection])
    
    style Start fill:#90EE90
    style End fill:#90EE90
    style MarkHFO fill:#87CEEB
    style SetThreshold fill:#DDA0DD
    style ValidatePeaks fill:#FFB6C1
```

## 5. WebSocket Communication Flow

This diagram shows the real-time update mechanism using WebSocket connections.

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant WSClient
    participant APIGateway
    participant Lambda
    participant DynamoDB
    participant StepFunction
    
    User->>Frontend: Start Analysis
    Frontend->>APIGateway: POST /analysis/start
    APIGateway->>Lambda: Start Analysis Handler
    Lambda->>DynamoDB: Create Analysis Record
    Lambda->>StepFunction: Start Execution
    Lambda-->>Frontend: Return Analysis ID
    
    Frontend->>WSClient: Connect WebSocket
    WSClient->>APIGateway: WSS Connection
    APIGateway->>Lambda: Connection Handler
    Lambda->>DynamoDB: Store Connection ID
    Lambda-->>WSClient: Connection Established
    
    WSClient->>APIGateway: Subscribe to Analysis
    APIGateway->>Lambda: Subscribe Handler
    Lambda->>DynamoDB: Create Subscription
    Lambda-->>WSClient: Subscription Confirmed
    
    loop Analysis Progress
        StepFunction->>Lambda: Task Update
        Lambda->>Lambda: Calculate Progress %
        Lambda->>DynamoDB: Get Subscriptions
        DynamoDB-->>Lambda: Connection IDs
        Lambda->>APIGateway: Broadcast Update
        APIGateway->>WSClient: Progress Event
        WSClient->>Frontend: Update UI
        Frontend->>User: Show Progress
    end
    
    StepFunction->>Lambda: Analysis Complete
    Lambda->>APIGateway: Broadcast Complete
    APIGateway->>WSClient: Complete Event
    WSClient->>Frontend: Show Results
    Frontend->>User: Enable Download
    
    Note over WSClient,APIGateway: Keep-Alive Ping/Pong every 30s
    
    alt Connection Lost
        WSClient->>WSClient: Detect Disconnect
        WSClient->>WSClient: Queue Messages
        WSClient->>APIGateway: Reconnect (Exponential Backoff)
        APIGateway->>Lambda: New Connection
        Lambda->>DynamoDB: Update Connection
        WSClient->>WSClient: Flush Message Queue
    end
    
    User->>Frontend: Close Page
    Frontend->>WSClient: Disconnect
    WSClient->>APIGateway: Close Connection
    APIGateway->>Lambda: Disconnect Handler
    Lambda->>DynamoDB: Remove Connection
    Lambda->>DynamoDB: Remove Subscriptions
```

## 6. Data Storage Structure

This diagram shows the organization of data in S3 and DynamoDB.

```mermaid
graph TB
    subgraph "S3 Bucket Structure"
        S3Root[biormika-data/]
        
        S3Root --> Users[users/]
        Users --> UserID[{userId}/]
        
        UserID --> Files[files/]
        UserID --> Temp[temp/]
        UserID --> Results[results/]
        
        Files --> FileID[{fileId}/]
        FileID --> OriginalEDF[original.edf<br/>Patient's uploaded file]
        
        Temp --> TempAnalysisID[{analysisId}/]
        TempAnalysisID --> ProcessedData[edf_data.npz<br/>Processed numpy array]
        TempAnalysisID --> HFOResults[hfo_results.json<br/>Detection results]
        TempAnalysisID --> TempPlots[visualization_*.png<br/>Temporary plots]
        
        Results --> FinalAnalysisID[{analysisId}/]
        FinalAnalysisID --> FinalResults[results.json<br/>Complete analysis]
        FinalAnalysisID --> Report[report.pdf<br/>Generated PDF]
        FinalAnalysisID --> Visualizations[visualization_*.png<br/>Final plots]
    end
    
    subgraph "DynamoDB Tables"
        subgraph "biormika-files Table"
            FilesTable[Primary Key Structure]
            FilesTable --> FilePK[PK: FILE#{fileId}]
            FilesTable --> FileSK[SK: METADATA]
            FilesTable --> FileAttrs[Attributes:<br/>- userId<br/>- fileName<br/>- fileSize<br/>- uploadDate<br/>- status<br/>- channelCount<br/>- duration<br/>- samplingRate]
        end
        
        subgraph "biormika-analysis Table"
            AnalysisTable[Primary Key Structure]
            AnalysisTable --> AnalysisPK[PK: ANALYSIS#{analysisId}]
            AnalysisTable --> AnalysisSK[SK: METADATA]
            AnalysisTable --> AnalysisAttrs[Attributes:<br/>- userId<br/>- fileId<br/>- status<br/>- parameters<br/>- startTime<br/>- endTime<br/>- resultUrl]
        end
        
        subgraph "biormika-websocket Table"
            WSTable[Connection Management]
            WSTable --> ConnPK[PK: CONNECTION]
            WSTable --> ConnSK[SK: CONN#{connectionId}]
            WSTable --> ConnAttrs[Attributes:<br/>- userId<br/>- ttl]
            
            WSTable --> SubPK[PK: ANALYSIS#{analysisId}]
            WSTable --> SubSK[SK: SUBSCRIPTION#{connectionId}]
            WSTable --> SubAttrs[Attributes:<br/>- userId<br/>- subscribedAt]
        end
    end
    
    style S3Root fill:#FFE6E6
    style FilesTable fill:#E6F3FF
    style AnalysisTable fill:#E6FFE6
    style WSTable fill:#FFFFE6
```

## 7. Frontend State Management

This diagram shows the Redux state management flow and component interactions.

```mermaid
graph TB
    subgraph "Redux Store Structure"
        Store[Redux Store]
        
        Store --> AuthSlice[auth<br/>- user<br/>- tokens<br/>- isAuthenticated]
        Store --> FilesSlice[files<br/>- fileList<br/>- uploadProgress<br/>- currentFile]
        Store --> AnalysisSlice[analysis<br/>- currentAnalysis<br/>- analysisHistory<br/>- parameters]
        Store --> UISlice[ui<br/>- loading<br/>- errors<br/>- notifications]
    end
    
    subgraph "Component Layer"
        LoginComp[Login Component]
        FileUpload[File Upload Component]
        AnalysisComp[Analysis Component]
        Dashboard[Dashboard Component]
    end
    
    subgraph "Service Layer"
        AuthService[auth.service.ts]
        FilesService[files.service.ts]
        AnalysisService[analysis.service.ts]
        WSService[websocket.service.ts]
    end
    
    subgraph "Middleware"
        AuthMiddleware[Auth Interceptor]
        ErrorMiddleware[Error Handler]
        PersistMiddleware[Redux Persist]
    end
    
    subgraph "Hooks"
        UseAuth[useAuth()]
        UseFiles[useFiles()]
        UseAnalysis[useAnalysis()]
        UseWebSocket[useWebSocket()]
    end
    
    LoginComp --> UseAuth
    FileUpload --> UseFiles
    AnalysisComp --> UseAnalysis
    Dashboard --> UseFiles
    Dashboard --> UseAnalysis
    
    UseAuth --> AuthSlice
    UseFiles --> FilesSlice
    UseAnalysis --> AnalysisSlice
    UseWebSocket --> UISlice
    
    AuthService --> AuthSlice
    FilesService --> FilesSlice
    AnalysisService --> AnalysisSlice
    WSService --> UISlice
    
    AuthSlice --> PersistMiddleware
    PersistMiddleware --> LocalStorage[LocalStorage]
    
    AuthSlice --> AuthMiddleware
    AuthMiddleware --> AxiosInstance[Axios Instance]
    
    ErrorMiddleware --> UISlice
    
    style Store fill:#DDA0DD
    style "Component Layer" fill:#90EE90
    style "Service Layer" fill:#87CEEB
    style Middleware fill:#FFB6C1
    style Hooks fill:#FFFFE6
```

## 8. API Request Flow

This diagram shows the complete request lifecycle including authentication and error handling.

```mermaid
sequenceDiagram
    participant Component
    participant Hook
    participant Service
    participant Axios
    participant AuthInterceptor
    participant ErrorInterceptor
    participant API
    participant Redux
    
    Component->>Hook: Call useFiles()
    Hook->>Service: uploadFile(file)
    Service->>Axios: POST /files/upload/init
    
    Axios->>AuthInterceptor: Request Interceptor
    AuthInterceptor->>Redux: Get Auth Token
    Redux-->>AuthInterceptor: Access Token
    AuthInterceptor->>AuthInterceptor: Add Authorization Header
    AuthInterceptor->>API: HTTP Request
    
    alt Success Response
        API-->>Axios: 200 OK + Data
        Axios-->>Service: Response Data
        Service->>Redux: Update State
        Service-->>Hook: Return Success
        Hook-->>Component: Update UI
    else Token Expired (401)
        API-->>ErrorInterceptor: 401 Unauthorized
        ErrorInterceptor->>ErrorInterceptor: Queue Original Request
        ErrorInterceptor->>Service: Call refreshToken()
        Service->>API: POST /auth/refresh
        API-->>Service: New Tokens
        Service->>Redux: Update Tokens
        ErrorInterceptor->>ErrorInterceptor: Retry Queued Requests
        ErrorInterceptor->>API: Retry with New Token
        API-->>Axios: 200 OK + Data
        Axios-->>Service: Response Data
        Service-->>Hook: Return Success
        Hook-->>Component: Update UI
    else Refresh Failed
        API-->>ErrorInterceptor: 401 on Refresh
        ErrorInterceptor->>Redux: Clear Auth State
        ErrorInterceptor->>Router: Redirect to Login
    else Other Error
        API-->>ErrorInterceptor: Error Response
        ErrorInterceptor->>ErrorInterceptor: Parse Error
        ErrorInterceptor->>Redux: Set Error State
        ErrorInterceptor->>Toast: Show Error Message
        ErrorInterceptor-->>Service: Throw Error
        Service-->>Hook: Return Error
        Hook-->>Component: Show Error UI
    end
    
    Note over Component,API: All requests follow this pattern
    Note over ErrorInterceptor: Handles rate limiting with retry
    Note over AuthInterceptor: Manages token lifecycle
```

## Summary

These diagrams provide a comprehensive view of the Biormika platform's architecture and flows:

1. **Authentication** - Secure multi-method authentication with automatic token management
2. **File Processing** - Robust upload and analysis pipeline with real-time updates
3. **System Architecture** - Scalable serverless design with clear separation of concerns
4. **HFO Algorithm** - Sophisticated detection with multiple validation stages
5. **WebSocket** - Real-time bidirectional communication with reconnection support
6. **Data Storage** - Organized structure for efficient data management
7. **State Management** - Centralized Redux store with persistence
8. **API Flow** - Comprehensive request handling with automatic retry and error management

Each diagram can be rendered using any Mermaid-compatible viewer or documentation tool.