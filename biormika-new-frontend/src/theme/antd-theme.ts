import type { ThemeConfig } from 'antd'

export const antdTheme: ThemeConfig = {
  token: {
    // Primary color matching Tailwind's blue-500
    colorPrimary: '#3b82f6',
    
    // Border radius
    borderRadius: 6,
    
    // Font family
    fontFamily: 'Inter, system-ui, sans-serif',
    
    // Success, warning, error colors
    colorSuccess: '#10b981',
    colorWarning: '#f59e0b',
    colorError: '#ef4444',
    
    // Gray colors
    colorTextBase: '#111827',
    colorBgBase: '#ffffff',
    
    // Spacing
    controlHeight: 32,
    
    // Animation
    motionDurationSlow: '0.3s',
    motionDurationMid: '0.2s',
    motionDurationFast: '0.1s',
  },
  components: {
    Button: {
      colorPrimary: '#3b82f6',
      algorithm: true,
    },
    Input: {
      colorBorder: '#d1d5db',
      borderRadius: 6,
    },
    Card: {
      borderRadius: 8,
      boxShadowTertiary: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    },
    Menu: {
      itemBorderRadius: 6,
    },
    Table: {
      borderRadius: 8,
    },
  },
}