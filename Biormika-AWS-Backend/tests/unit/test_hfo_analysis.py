import pytest
import numpy as np
from analysis.core.hfo_analysis import (
    iirnotch_hfo,
    run_hfo_detection,
    estimate_frequency,
    find_synchronized_hfos
)


@pytest.mark.unit
class TestHFOAnalysis:

    def test_iirnotch_filter_design(self):
        Wo = 60 / (256 / 2)
        BW = Wo / 30

        num, den = iirnotch_hfo(Wo, BW)

        assert len(num) == 3
        assert len(den) == 3
        assert np.isclose(num[0], num[2])
        assert np.isclose(den[0], 1.0)

    def test_frequency_estimation(self):
        sampling_rate = 256
        duration = 0.1
        frequency = 100

        t = np.linspace(0, duration, int(sampling_rate * duration))
        signal = np.sin(2 * np.pi * frequency * t)

        estimated_freq = estimate_frequency(signal, sampling_rate)

        assert abs(estimated_freq - frequency) < 10

    def test_hfo_detection_empty_data(self):
        empty_data = np.zeros((2, 1000))
        sampling_rate = 256
        analysis_params = {
            'lowCutoffFilter': 80,
            'highCutoffFilter': 250,
            'thresholdSettings': {
                'amplitude1': 5,
                'amplitude2': 3,
                'duration': 10,
                'peaks1': 6,
                'peaks2': 3
            }
        }

        results = run_hfo_detection(empty_data, sampling_rate, analysis_params)

        assert results['total_hfos'] == 0
        assert results['hfo_rate'] == 0
        assert len(results['channels']) == 2

    def test_hfo_detection_with_synthetic_hfo(self):
        sampling_rate = 256
        duration = 5
        num_samples = sampling_rate * duration

        data = np.random.randn(1, num_samples) * 0.1

        hfo_start = int(2 * sampling_rate)
        hfo_duration = int(0.02 * sampling_rate)
        t_hfo = np.linspace(0, 0.02, hfo_duration)
        hfo_signal = 3 * np.sin(2 * np.pi * 150 * t_hfo)
        data[0, hfo_start:hfo_start + hfo_duration] += hfo_signal

        analysis_params = {
            'lowCutoffFilter': 80,
            'highCutoffFilter': 250,
            'thresholdSettings': {
                'amplitude1': 3,
                'amplitude2': 2,
                'duration': 10,
                'peaks1': 3,
                'peaks2': 2
            }
        }

        results = run_hfo_detection(data, sampling_rate, analysis_params)

        assert results['total_hfos'] >= 0
        assert 'channels' in results
        assert 'all_events' in results

    def test_synchronized_hfo_detection(self):
        events = [
            {'channel': 0, 'start_time': 1.000, 'end_time': 1.020},
            {'channel': 1, 'start_time': 1.005, 'end_time': 1.025},
            {'channel': 2, 'start_time': 2.000, 'end_time': 2.020},
            {'channel': 0, 'start_time': 3.000, 'end_time': 3.020}
        ]

        sync_threshold = 0.010
        sync_events = find_synchronized_hfos(events, sync_threshold)

        assert len(sync_events) >= 1
        assert sync_events[0]['channels'] == [0, 1]

    def test_parameter_validation(self):
        data = np.random.randn(2, 1000)
        sampling_rate = 256

        invalid_params = {
            'lowCutoffFilter': 300,
            'highCutoffFilter': 80,
            'thresholdSettings': {}
        }

        results = run_hfo_detection(data, sampling_rate, invalid_params)

        assert 'channels' in results
        assert isinstance(results['total_hfos'], int)
