import type { ReactNode } from 'react'
import { brandColors } from '../constants/colors'

interface PageHeaderProps {
  title: string
  subtitle?: string
  variant?: 'brand' | 'page' | 'processing'
  className?: string
  children?: ReactNode
}

export function PageHeader({ 
  title, 
  subtitle, 
  variant = 'page', 
  className = '',
  children 
}: PageHeaderProps) {
  const getHeaderStyles = () => {
    switch (variant) {
      case 'brand':
        return {
          titleClass: 'text-4xl font-bold mb-3',
          titleColor: brandColors.text.primary,
          subtitleClass: 'text-lg font-medium',
          subtitleColor: brandColors.primary,
          containerClass: 'text-center mb-12'
        }
      case 'processing':
        return {
          titleClass: 'text-4xl font-bold mb-8',
          titleColor: brandColors.text.primary,
          subtitleClass: 'text-lg',
          subtitleColor: brandColors.text.secondary,
          containerClass: 'text-center mb-12'
        }
      default: // page
        return {
          titleClass: 'text-3xl font-bold mb-3',
          titleColor: brandColors.text.primary,
          subtitleClass: 'text-lg',
          subtitleColor: brandColors.text.secondary,
          containerClass: 'text-center mb-8'
        }
    }
  }

  const styles = getHeaderStyles()

  return (
    <div className={`${styles.containerClass} ${className}`}>
      <h1 
        className={styles.titleClass}
        style={{ color: styles.titleColor }}
      >
        {title}
      </h1>
      {subtitle && (
        <p 
          className={styles.subtitleClass}
          style={{ color: styles.subtitleColor }}
        >
          {subtitle}
        </p>
      )}
      {children}
    </div>
  )
}