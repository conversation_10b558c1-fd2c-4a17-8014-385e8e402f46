import { Button } from 'antd'
import type { ReactNode } from 'react'
import { brandColors } from '../constants/colors'

interface PrimaryButtonProps {
  children: ReactNode
  loading?: boolean
  htmlType?: 'button' | 'submit' | 'reset'
  onClick?: () => void
  className?: string
  fullWidth?: boolean
}

export function PrimaryButton({ 
  children, 
  loading = false, 
  htmlType = 'button',
  onClick,
  className = '',
  fullWidth = true
}: PrimaryButtonProps) {
  return (
    <Button
      type="primary"
      htmlType={htmlType}
      loading={loading}
      onClick={onClick}
      style={{
        backgroundColor: brandColors.actionButton,
        borderColor: brandColors.actionButton,
      }}
      className={`
        ${fullWidth ? 'w-full' : ''} 
        h-12 bg-brand-action-button border-brand-action-button
        hover:bg-slate-800 hover:border-slate-800 
        rounded-lg font-medium text-base 
        ${className}
      `}
    >
      {children}
    </Button>
  )
}