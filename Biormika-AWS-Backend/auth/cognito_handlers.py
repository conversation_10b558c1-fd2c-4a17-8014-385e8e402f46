import os
import boto3
from botocore.exceptions import ClientError

cognito = boto3.client('cognito-idp')
USER_POOL_ID = os.environ.get('COGNITO_USER_POOL_ID')
CLIENT_ID = os.environ.get('COGNITO_CLIENT_ID')

def create_user(email, password, name):
    try:
        response = cognito.admin_create_user(
            UserPoolId=USER_POOL_ID,
            Username=email,
            UserAttributes=[
                {'Name': 'email', 'Value': email},
                {'Name': 'name', 'Value': name},
                {'Name': 'email_verified', 'Value': 'true'}
            ],
            TemporaryPassword=password,
            MessageAction='SUPPRESS'
        )
        
        cognito.admin_set_user_password(
            UserPoolId=USER_POOL_ID,
            Username=email,
            Password=password,
            Permanent=True
        )
        
        return response, None
    except cognito.exceptions.UsernameExistsException:
        return None, ("User already exists", 409)
    except ClientError as e:
        print(f"Cognito create user error: {e}")
        return None, ("Failed to create user", 500)

def authenticate_user(email, password):
    try:
        auth_response = cognito.admin_initiate_auth(
            UserPoolId=USER_POOL_ID,
            ClientId=CLIENT_ID,
            AuthFlow='ADMIN_NO_SRP_AUTH',
            AuthParameters={
                'USERNAME': email,
                'PASSWORD': password
            }
        )
        
        return auth_response, None
    except cognito.exceptions.UserNotFoundException:
        return None, ("User not found", 404)
    except cognito.exceptions.NotAuthorizedException:
        return None, ("Invalid credentials", 401)
    except ClientError as e:
        print(f"Cognito auth error: {e}")
        return None, ("Authentication service error", 500)

def get_user_info(username):
    try:
        response = cognito.admin_get_user(
            UserPoolId=USER_POOL_ID,
            Username=username
        )
        
        attributes = {attr['Name']: attr['Value'] for attr in response['UserAttributes']}
        return attributes, None
    except ClientError as e:
        print(f"Cognito get user error: {e}")
        return None, ("Failed to get user info", 500)

def refresh_user_token(refresh_token):
    try:
        response = cognito.admin_initiate_auth(
            UserPoolId=USER_POOL_ID,
            ClientId=CLIENT_ID,
            AuthFlow='REFRESH_TOKEN_AUTH',
            AuthParameters={
                'REFRESH_TOKEN': refresh_token
            }
        )
        
        return response, None
    except ClientError as e:
        print(f"Token refresh error: {e}")
        return None, ("Invalid refresh token", 401)