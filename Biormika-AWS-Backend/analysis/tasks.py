import json
import os
import boto3
import numpy as np
from datetime import datetime
from analysis.edf.reader import read_edf_from_s3, extract_channels_for_montage
from analysis.core.hfo_analysis import run_hfo_detection
from visualization.plotter import generate_hfo_plots
from visualization.pdf_generator import generate_pdf_report

s3 = boto3.client('s3')
S3_BUCKET = os.environ.get('S3_BUCKET')


def read_edf_file(event, context):
    try:
        s3_key = event['s3Key']
        analysis_id = event['analysisId']
        user_id = event['userId']
        settings = event.get('settings', {})

        update_analysis_progress(analysis_id, user_id, 'reading_file', 10)

        hdr, record = read_edf_from_s3(s3_key)

        montage_settings = settings.get('montageSelection', {})
        if montage_settings.get('bipolar'):
            montage_type = 'bipolar'
        elif montage_settings.get('average'):
            montage_type = 'average'
        elif montage_settings.get('referential'):
            montage_type = 'referential'
        else:
            montage_type = None

        if montage_type:
            selected_channels = settings.get('selectedChannels', [])
            hdr, record = extract_channels_for_montage(
                hdr, record, montage_type, selected_channels
            )

        update_analysis_progress(analysis_id, user_id, 'file_read_complete', 25)

        edf_data_key = f"users/{user_id}/temp/{analysis_id}/edf_data.npz"
        save_numpy_to_s3(record, edf_data_key)

        return {
            'statusCode': 200,
            'analysisId': analysis_id,
            'userId': user_id,
            'settings': settings,
            'edfDataKey': edf_data_key,
            'header': {
                'samplingRate': float(hdr['frequency'][0]),
                'channels': hdr['label'],
                'duration': float(hdr['records'] * hdr['duration']),
                'numChannels': len(hdr['label'])
            }
        }

    except Exception as e:
        print(f"Error reading EDF file: {e}")
        update_analysis_progress(analysis_id, user_id, 'error', 0, str(e))
        raise e


def detect_hfo(event, context):
    try:
        analysis_id = event['analysisId']
        user_id = event['userId']
        settings = event['settings']
        edf_data_key = event['edfDataKey']
        header = event['header']

        update_analysis_progress(analysis_id, user_id, 'detecting_hfos', 40)

        eeg_data = load_numpy_from_s3(edf_data_key)
        sampling_rate = header['samplingRate']

        hfo_results = run_hfo_detection(eeg_data, sampling_rate, settings)

        update_analysis_progress(analysis_id, user_id, 'hfo_detection_complete', 70)

        results_key = f"users/{user_id}/temp/{analysis_id}/hfo_results.json"
        s3.put_object(
            Bucket=S3_BUCKET,
            Key=results_key,
            Body=json.dumps(hfo_results, default=str),
            ContentType='application/json'
        )

        return {
            'statusCode': 200,
            'analysisId': analysis_id,
            'userId': user_id,
            'settings': settings,
            'edfDataKey': edf_data_key,
            'header': header,
            'resultsKey': results_key,
            'hfoSummary': {
                'totalHfos': hfo_results['total_hfos'],
                'hfoRate': hfo_results['hfo_rate'],
                'analysisDuration': hfo_results['analysis_duration']
            }
        }

    except Exception as e:
        print(f"Error detecting HFOs: {e}")
        update_analysis_progress(analysis_id, user_id, 'error', 0, str(e))
        raise e


def generate_visualization(event, context):
    try:
        analysis_id = event['analysisId']
        user_id = event['userId']
        edf_data_key = event['edfDataKey']
        results_key = event['resultsKey']
        header = event['header']

        update_analysis_progress(analysis_id, user_id, 'generating_visualizations', 85)

        eeg_data = load_numpy_from_s3(edf_data_key)

        response = s3.get_object(Bucket=S3_BUCKET, Key=results_key)
        hfo_results = json.loads(response['Body'].read())

        visualization_keys = generate_hfo_plots(
            eeg_data,
            hfo_results,
            header,
            analysis_id,
            user_id
        )

        update_analysis_progress(analysis_id, user_id, 'visualizations_complete', 95)

        return {
            'statusCode': 200,
            'analysisId': analysis_id,
            'userId': user_id,
            'resultsKey': results_key,
            'visualizationKeys': visualization_keys,
            'hfoSummary': event.get('hfoSummary', {})
        }

    except Exception as e:
        print(f"Error generating visualizations: {e}")
        update_analysis_progress(analysis_id, user_id, 'error', 0, str(e))
        raise e


def process_results(event, context):
    try:
        analysis_id = event['analysisId']
        user_id = event['userId']
        results_key = event['resultsKey']
        visualization_keys = event.get('visualizationKeys', [])
        hfo_summary = event.get('hfoSummary', {})

        update_analysis_progress(analysis_id, user_id, 'finalizing_results', 98)

        response = s3.get_object(Bucket=S3_BUCKET, Key=results_key)
        hfo_results = json.loads(response['Body'].read())

        report_key = generate_pdf_report(
            hfo_results,
            visualization_keys,
            analysis_id,
            user_id
        )

        final_results_key = f"users/{user_id}/results/{analysis_id}/results.json"
        final_results = {
            'analysisId': analysis_id,
            'completedAt': datetime.utcnow().isoformat(),
            'summary': hfo_summary,
            'detailedResults': hfo_results,
            'reportKey': report_key,
            'visualizationKeys': visualization_keys,
            'num_visualizations': len(visualization_keys)
        }

        s3.put_object(
            Bucket=S3_BUCKET,
            Key=final_results_key,
            Body=json.dumps(final_results, default=str),
            ContentType='application/json'
        )

        for i, viz_key in enumerate(visualization_keys):
            source_key = viz_key
            dest_key = f"users/{user_id}/results/{analysis_id}/visualization_{i}.png"
            s3.copy_object(
                CopySource={'Bucket': S3_BUCKET, 'Key': source_key},
                Bucket=S3_BUCKET,
                Key=dest_key
            )

        source_report = report_key
        dest_report = f"users/{user_id}/results/{analysis_id}/report.pdf"
        s3.copy_object(
            CopySource={'Bucket': S3_BUCKET, 'Key': source_report},
            Bucket=S3_BUCKET,
            Key=dest_report
        )

        update_analysis_progress(analysis_id, user_id, 'completed', 100)

        cleanup_temp_files(analysis_id, user_id)

        return {
            'statusCode': 200,
            'analysisId': analysis_id,
            'status': 'completed',
            'resultsKey': final_results_key,
            'message': 'Analysis completed successfully'
        }

    except Exception as e:
        print(f"Error processing results: {e}")
        update_analysis_progress(analysis_id, user_id, 'error', 0, str(e))
        raise e


def update_analysis_progress(analysis_id, user_id, status, progress, error=None):
    try:
        from storage.metadata import table

        update_data = {
            'status': status,
            'progress': progress,
            'lastUpdated': datetime.utcnow().isoformat()
        }

        update_expression = 'SET #status = :status, #progress = :progress, #lastUpdated = :lastUpdated'
        expression_names = {
            '#status': 'status',
            '#progress': 'progress',
            '#lastUpdated': 'lastUpdated'
        }
        expression_values = {
            ':status': status,
            ':progress': progress,
            ':lastUpdated': update_data['lastUpdated']
        }

        if error:
            update_expression += ', #error = :error'
            expression_names['#error'] = 'error'
            expression_values[':error'] = error

        table.update_item(
            Key={
                'PK': f"USER#{user_id}",
                'SK': f"ANALYSIS#{analysis_id}"
            },
            UpdateExpression=update_expression,
            ExpressionAttributeNames=expression_names,
            ExpressionAttributeValues=expression_values
        )
    except Exception as e:
        print(f"Error updating progress: {e}")


def save_numpy_to_s3(array, s3_key):
    import tempfile

    with tempfile.NamedTemporaryFile(suffix='.npz') as tmp:
        np.savez_compressed(tmp.name, data=array)
        tmp.seek(0)
        s3.upload_fileobj(tmp, S3_BUCKET, s3_key)


def load_numpy_from_s3(s3_key):
    import tempfile

    with tempfile.NamedTemporaryFile(suffix='.npz') as tmp:
        s3.download_fileobj(S3_BUCKET, s3_key, tmp)
        tmp.seek(0)
        data = np.load(tmp)
        return data['data']


def cleanup_temp_files(analysis_id, user_id):
    try:
        prefix = f"users/{user_id}/temp/{analysis_id}/"

        response = s3.list_objects_v2(
            Bucket=S3_BUCKET,
            Prefix=prefix
        )

        if 'Contents' in response:
            objects = [{'Key': obj['Key']} for obj in response['Contents']]

            s3.delete_objects(
                Bucket=S3_BUCKET,
                Delete={'Objects': objects}
            )
    except Exception as e:
        print(f"Error cleaning up temp files: {e}")
