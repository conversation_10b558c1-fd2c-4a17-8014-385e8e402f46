import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { lazy, Suspense } from 'react'
import { routes } from './router/routes'
import { AuthGuard } from './router/guards/AuthGuard'
import { GuestGuard } from './router/guards/GuestGuard'

// Dynamic imports for all page components
const Dashboard = lazy(() => import('./pages/Dashboard'))
const ConfigurationParameters = lazy(() => import('./pages/ConfigurationParameters'))
const Analysis = lazy(() => import('./pages/Analysis'))
const Login = lazy(() => import('./pages/Login'))
const SignUp = lazy(() => import('./pages/SignUp'))
const ForgotPassword = lazy(() => import('./pages/ForgotPassword'))
const RequestDemo = lazy(() => import('./pages/RequestDemo'))
const NotFound = lazy(() => import('./pages/NotFound'))

// Component mapping for dynamic routing
const componentMap = {
  Dashboard,
  ConfigurationParameters,
  Analysis,
  Login,
  SignUp,
  ForgotPassword,
  RequestDemo,
  NotFound,
}

// Loading fallback component
function LoadingFallback() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
    </div>
  )
}

// Route wrapper that applies appropriate guards
function RouteWrapper({
  children,
  requireAuth,
  guestOnly,
}: {
  children: React.ReactNode
  requireAuth?: boolean
  guestOnly?: boolean
}) {
  if (requireAuth) {
    return <AuthGuard>{children}</AuthGuard>
  }

  if (guestOnly) {
    return <GuestGuard>{children}</GuestGuard>
  }

  return <>{children}</>
}

export function AppRouting() {
  return (
    <BrowserRouter>
      <Suspense fallback={<LoadingFallback />}>
        <Routes>
          {routes.map((route) => {
            if (route.path === '*') {
              return <Route key={route.path} path="*" element={<Navigate to="/404" replace />} />
            }

            const Component = componentMap[route.element as keyof typeof componentMap]

            if (!Component) {
              console.warn(`Component ${route.element} not found in componentMap`)
              return null
            }

            return (
              <Route
                key={route.path}
                path={route.path}
                element={
                  <RouteWrapper requireAuth={route.requireAuth} guestOnly={route.guestOnly}>
                    <Component />
                  </RouteWrapper>
                }
              />
            )
          })}

          {/* 404 route */}
          <Route path="/404" element={<NotFound />} />
        </Routes>
      </Suspense>
    </BrowserRouter>
  )
}
