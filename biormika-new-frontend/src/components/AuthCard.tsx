import type { ReactNode } from 'react'

interface AuthCardProps {
  children: ReactNode
  className?: string
  width?: 'sm' | 'md' | 'lg'
}

export function AuthCard({ children, className = '', width = 'md' }: AuthCardProps) {
  const widthClasses = {
    sm: 'max-w-md',
    md: 'max-w-md',
    lg: 'max-w-2xl',
  }

  return (
    <div className={`w-full ${widthClasses[width]}`}>
      <div className={`bg-white rounded-2xl shadow-xl p-8 ${className}`}>{children}</div>
    </div>
  )
}
