
  Installation Steps and Starting the Application

  Backend Setup (Biormika-AWS-Backend)

  1. Install Dependencies:
  cd Biormika-AWS-Backend
  npm install                    # Install Node.js dependencies
  pip install -r requirements.txt # Install Python dependencies
  2. Local Development Setup:
  # No additional local services needed - serverless offline handles everything
  3. Configure Environment:
  cp .env.local .env            # Copy local environment configuration
  4. Start Backend Server:
  npm run offline               # Starts Serverless offline
  # API: http://localhost:3001
  # WebSocket: ws://localhost:3002

  Frontend Setup (biormika-new-frontend)

  1. Install Dependencies:
  cd biormika-new-frontend
  npm install
  2. Configure Environment:
  cp .env.example .env          # Copy and modify environment variables
  3. Start Frontend Server:
  npm run dev                   # Starts Vite development server
  # Frontend: http://localhost:5173

  AWS Resources Being Deployed

  When deploying to AWS (not local), the serverless.yml configures these resources:

  1. Lambda Functions:
    - Authentication: login, signup, refresh token
    - File Management: init upload, validate file
    - Analysis: start analysis, get status/results
    - Step Function Tasks: read EDF, detect HFO, generate visualization, process results
    - WebSocket: connect, disconnect, default handler
  2. Storage & Database:
    - S3 Bucket: For EDF file storage and analysis results
    - DynamoDB Table: For metadata (users, files, analysis records)
  3. Authentication:
    - Cognito User Pool: User authentication and management
    - Cognito User Pool Client: Application authentication
  4. Processing & Orchestration:
    - Step Functions State Machine: Orchestrates the HFO analysis workflow
    - IAM Roles: Execution permissions for services
  5. API & Communication:
    - API Gateway: REST API endpoints
    - WebSocket API: Real-time progress updates

  Running Locally with Serverless Offline

  Recommended Local Development Setup:

  1. Backend with Serverless Offline:
    - Uses serverless offline for local development
    - No AWS charges during local development
    - Full API functionality at http://localhost:3001
    - WebSocket support at ws://localhost:3002
  2. Frontend Configuration for Local Backend:
  Update your frontend .env file:
  VITE_API_BASE_URL=http://localhost:3001/dev
  VITE_WEBSOCKET_URL=ws://localhost:3002
  VITE_COGNITO_USER_POOL_ID=local_pool_id
  VITE_COGNITO_CLIENT_ID=local_client_id
  3. Advantages of Local Development:
    - No AWS costs - Everything runs locally
    - Faster development - No deployment delays
    - Full debugging - Direct access to logs and services
    - Isolated environment - No risk to production data
  4. Local Development Features:
    - Lambda functions run locally
    - API Gateway endpoints simulated
    - File uploads handled locally
    - WebSocket connections supported

  Development Workflow:

  1. Start Services:
  # Terminal 1 - Backend
  cd Biormika-AWS-Backend
  npm run offline

  # Terminal 2 - Frontend
  cd biormika-new-frontend
  npm run dev
  2. Access Applications:
    - Frontend: http://localhost:5173
    - API: http://localhost:3001/dev
  3. Testing:
    - Backend: npm test or pytest tests/
    - Frontend: npm run test:api (API integration tests)

  This setup provides a complete local development environment without requiring AWS
  deployment, perfect for development and testing.
