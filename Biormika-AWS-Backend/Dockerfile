FROM public.ecr.aws/lambda/python:3.9

# Install system dependencies required for scientific libraries
RUN yum install -y gcc gcc-c++ libgfortran && \
    yum clean all && \
    rm -rf /var/cache/yum

# Copy requirements file
COPY requirements.txt ${LAMBDA_TASK_ROOT}/

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy function code
COPY . ${LAMBDA_TASK_ROOT}/

# Set the CMD to your handler 
# This will be overridden by individual function configurations
CMD ["lambda_function.handler"]