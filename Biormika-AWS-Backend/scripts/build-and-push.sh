#!/bin/bash

set -e

# Configuration
SERVICE_NAME="biormika-hfo-backend"
STAGE=${1:-dev}
REGION=${2:-us-east-1}
AWS_PROFILE=${3:-kirtan-default}

# Get AWS account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --profile $AWS_PROFILE --query Account --output text)

# ECR repository name
ECR_REPO_NAME="${SERVICE_NAME}-${STAGE}"
ECR_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/${ECR_REPO_NAME}"

echo "🚀 Building and pushing Lambda container image..."
echo "   Service: ${SERVICE_NAME}"
echo "   Stage: ${STAGE}"
echo "   Region: ${REGION}"
echo "   AWS Profile: ${AWS_PROFILE}"
echo "   ECR Repository: ${ECR_URI}"

# Create ECR repository if it doesn't exist
echo "📦 Checking ECR repository..."
aws ecr describe-repositories --repository-names ${ECR_REPO_NAME} --region ${REGION} --profile ${AWS_PROFILE} 2>/dev/null || \
aws ecr create-repository --repository-name ${ECR_REPO_NAME} --region ${REGION} --profile ${AWS_PROFILE}

# Get ECR login token
echo "🔐 Logging in to ECR..."
aws ecr get-login-password --region ${REGION} --profile ${AWS_PROFILE} | \
docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com

# Build Docker image
echo "🔨 Building Docker image..."
docker build -t ${ECR_REPO_NAME}:latest .

# Tag image for ECR
echo "🏷️  Tagging image for ECR..."
docker tag ${ECR_REPO_NAME}:latest ${ECR_URI}:latest
docker tag ${ECR_REPO_NAME}:latest ${ECR_URI}:${STAGE}

# Push to ECR
echo "⬆️  Pushing image to ECR..."
docker push ${ECR_URI}:latest
docker push ${ECR_URI}:${STAGE}

echo "✅ Docker image successfully pushed to ECR!"
echo "   Image URI: ${ECR_URI}:${STAGE}"