import numpy as np
from scipy.signal import butter, filtfilt, hilbert, find_peaks, spectrogram
from numpy import hamming
from scipy.fft import fft


def find_blanks(myEEG, orig_channel_labels):
    """Find discontinuities or blank periods in EEG signal and identify artifact channels."""
    orig_num_channels, orig_num_pts = myEEG.shape

    BS = []  # List of blank_start vectors for all channels
    BE = []  # List of blank_end vectors for all channels

    for j in range(orig_num_channels):
        data = myEEG[j, :]

        flag = 0
        blank_start = []
        blank_end = []
        ct = 0

        # Find areas where there are 3 identical data points in a row
        for i in range(orig_num_pts - 2):
            if data[i] == data[i + 1] == data[i + 2]:
                if flag == 0:
                    flag = 1
                    blank_start.append(i)
            else:
                if flag == 1:
                    flag = 0
                    blank_end.append(i + 1)
                    ct += 1

        if flag == 1:
            blank_end.append(orig_num_pts)

        # Eliminate all blanks that are less than 20 points (likely real data)
        i = 0
        while i < len(blank_start):
            if blank_end[i] - blank_start[i] + 1 < 20:
                del blank_start[i]
                del blank_end[i]
            else:
                i += 1

        BS.append(blank_start)
        BE.append(blank_end)

    num_blanks = []
    for j in range(orig_num_channels):
        num_blanks.append(len(BS[j]))

    true_num_blanks = int(np.median(num_blanks)) if num_blanks else 0

    artifact_channels = []
    for j in range(orig_num_channels):
        if num_blanks[j] != true_num_blanks:
            artifact_channels.append(orig_channel_labels[j])

    true_blank_start = []
    true_blank_end = []
    for j in range(orig_num_channels):
        if num_blanks[j] == true_num_blanks:
            true_blank_start = BS[j]
            true_blank_end = BE[j]
            break

    return true_blank_start, true_blank_end, artifact_channels


def iirnotch_hfo(Wo, BW, Ab=-3):
    Gb = 10 ** (-Ab / 20)
    Gb = np.clip(Gb, 0, 1)

    BW = BW * np.pi
    Wo = Wo * np.pi

    beta = (np.sqrt(np.abs(1 - Gb ** 2)) / Gb) * np.tan(BW / 2)
    gain = 1 / (1 + beta)

    num = gain * np.array([1, -2 * np.cos(Wo), 1])
    den = np.array([1, -2 * gain * np.cos(Wo), 2 * gain - 1])

    return num, den


def run_hfo_detection(eeg_data, sampling_rate, analysis_params):
    try:
        locutoff = analysis_params.get('lowCutoffFilter', 80)
        hicutoff = analysis_params.get('highCutoffFilter', 250)

        threshold_settings = analysis_params.get('thresholdSettings', {})
        thresh = threshold_settings.get('amplitude1', 5)
        thresh2 = threshold_settings.get('amplitude2', 3)
        min_HFO_len = threshold_settings.get('duration', 10)
        num_peaks = threshold_settings.get('peaks1', 6)
        num_peaks2 = threshold_settings.get('peaks2', 3)

        synchronization_settings = analysis_params.get(
            'synchronizationSettings', {})
        # min_break = synchronization_settings.get('temporalSync', 10)
        con_delay = synchronization_settings.get('spatialSync', 0)

        segment_settings = analysis_params.get('segmentSelectionSettings', {})
        analysis_start = segment_settings.get('startTime', 0)
        analysis_end = segment_settings.get('endTime', -1)

        if analysis_end == -1:
            analysis_end = len(eeg_data[0]) / sampling_rate

        sample_start = int(analysis_start * sampling_rate)
        sample_end = int(analysis_end * sampling_rate)

        if len(eeg_data.shape) == 1:
            eeg_data = eeg_data.reshape(1, -1)

        num_channels = eeg_data.shape[0]
        data_segment = eeg_data[:, sample_start:sample_end]

        notch_freq = 60
        Q = 30
        Wo = notch_freq / (sampling_rate / 2)
        BW = Wo / Q
        b_notch, a_notch = iirnotch_hfo(Wo, BW)

        nyquist = sampling_rate / 2
        low = locutoff / nyquist
        high = hicutoff / nyquist
        b_bandpass, a_bandpass = butter(3, [low, high], btype='band')

        hfo_results = {
            'channels': [],
            'total_hfos': 0,
            'hfo_rate': 0,
            'analysis_duration': analysis_end - analysis_start
        }

        all_hfo_events = []

        for ch in range(num_channels):
            channel_data = data_segment[ch, :]

            if notch_freq > 0:
                channel_data = filtfilt(b_notch, a_notch, channel_data)

            filtered_data = filtfilt(b_bandpass, a_bandpass, channel_data)

            analytic_signal = hilbert(filtered_data)
            envelope = np.abs(analytic_signal)

            baseline_window = int(2 * sampling_rate)
            baseline = np.median(envelope[:baseline_window])
            std_baseline = np.std(envelope[:baseline_window])

            threshold = baseline + thresh * std_baseline

            above_threshold = envelope > threshold

            changes = np.diff(np.concatenate(
                ([0], above_threshold.astype(int), [0])))
            starts = np.where(changes == 1)[0]
            ends = np.where(changes == -1)[0]

            channel_hfos = []

            for start, end in zip(starts, ends):
                duration_ms = (end - start) / sampling_rate * 1000

                if duration_ms >= min_HFO_len:
                    segment = filtered_data[start:end]
                    peaks, _ = find_peaks(segment)

                    if len(peaks) >= num_peaks:
                        peak_amplitudes = segment[peaks]
                        high_peaks = np.sum(peak_amplitudes > (
                            baseline + thresh2 * std_baseline))

                        if high_peaks >= num_peaks2:
                            hfo_event = {
                                'channel': ch,
                                'start_time': (start + sample_start) / sampling_rate,
                                'end_time': (end + sample_start) / sampling_rate,
                                'duration_ms': duration_ms,
                                'num_peaks': len(peaks),
                                'max_amplitude': np.max(envelope[start:end]),
                                'mean_frequency': estimate_frequency(
                                    segment, sampling_rate
                                )
                            }
                            channel_hfos.append(hfo_event)
                            all_hfo_events.append(hfo_event)

            hfo_results['channels'].append({
                'channel_index': ch,
                'hfo_count': len(channel_hfos),
                'hfo_rate': len(channel_hfos) / (analysis_end - analysis_start),
                'events': channel_hfos
            })

        hfo_results['total_hfos'] = len(all_hfo_events)
        hfo_results['hfo_rate'] = len(
            all_hfo_events) / (analysis_end - analysis_start)
        hfo_results['all_events'] = sorted(
            all_hfo_events, key=lambda x: x['start_time'])

        if con_delay > 0:
            hfo_results['synchronized_events'] = find_synchronized_hfos(
                all_hfo_events, con_delay / 1000)

        return hfo_results

    except Exception as e:
        print(f"HFO detection error: {e}")
        raise e


def estimate_frequency(signal, sampling_rate):
    try:
        fft = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1 / sampling_rate)

        positive_freqs = freqs[:len(freqs) // 2]
        positive_fft = np.abs(fft[:len(fft) // 2])

        peak_idx = np.argmax(positive_fft)
        peak_freq = positive_freqs[peak_idx]

        return peak_freq
    except BaseException:
        return 0


def find_synchronized_hfos(hfo_events, max_delay):
    synchronized_groups = []
    used_events = set()

    for i, event1 in enumerate(hfo_events):
        if i in used_events:
            continue

        group = [event1]
        used_events.add(i)

        for j, event2 in enumerate(hfo_events[i + 1:], i + 1):
            if j in used_events:
                continue

            if event2['channel'] != event1['channel']:
                time_diff = abs(event1['start_time'] - event2['start_time'])

                if time_diff <= max_delay:
                    group.append(event2)
                    used_events.add(j)

        if len(group) > 1:
            synchronized_groups.append({
                'channels': [e['channel'] for e in group],
                'events': group,
                'synchronization_delay': max([
                    abs(e['start_time'] - group[0]['start_time']) for e in group
                ])
            })

    return synchronized_groups


def calculate_HFO_characteristics(data, s_freq, noise_freq_removal, noise_freq_vector, hfo_freq, max_freq, pwr_thresh):
    """
    Returns characteristics of the given HFO.

    Args:
    - data: The EEG segment data representing the HFO.
    - s_freq: Sampling frequency.
    - noise_freq_removal: Set to 1 or 0 for noise removal.
    - noise_freq_vector: Frequencies to be removed if too close to noise frequencies.
    - hfo_freq: The threshold frequency for HFO.
    - max_freq: The maximum frequency to consider.
    - pwr_thresh: The power threshold for HFO detection.

    Returns:
    - flag: 1 if true HFO, 0 if too low frequency, 2 if too close to noise.
    - duration: Duration of HFO in milliseconds.
    - peak_power: Peak power from FFT.
    - total_power_hfo: Power in the HFO frequency range from FFT.
    - peak_freq: Peak frequency from FFT.
    - my_amp: Mean amplitude of the rectified signal.
    - max_freq: Maximum significant frequency in FFT.
    """

    MAX_FREQ = max_freq
    HFO_FREQ = hfo_freq
    peak_to_peak_freq = 1
    max_freq_thresh = 0.01  # Minimum fraction of peak power for frequency significance

    flag = 0  # Default to not a true HFO
    duration = (len(data) / s_freq) * 1000  # Duration in ms
    my_amp = np.mean(np.abs(data))  # Mean amplitude

    # FFT analysis
    FFT_length = 512
    if len(data) < FFT_length:
        FFT_length = len(data)

    # Apply Hamming window
    windowed_data = data * hamming(len(data))

    # Compute FFT
    Y = fft(windowed_data, FFT_length)
    myPower = np.abs(Y) ** 2
    freqs = np.fft.fftfreq(FFT_length, 1/s_freq)

    # Only consider positive frequencies
    half_length = FFT_length // 2
    myPower = myPower[:half_length]
    freqs = freqs[:half_length]

    # Find peak frequency and power
    peak_idx = np.argmax(myPower)
    peak_freq = freqs[peak_idx]
    peak_power = myPower[peak_idx]

    # Find maximum significant frequency
    max_freq_idx = np.where(myPower > max_freq_thresh * peak_power)[0]
    if len(max_freq_idx) > 0:
        max_freq = freqs[max_freq_idx[-1]]
    else:
        max_freq = peak_freq

    # Peak-to-peak frequency analysis
    peak_freq1 = 0
    if peak_to_peak_freq == 1:
        # Find peaks in the signal
        peaks, _ = find_peaks(data)
        if len(peaks) > 1:
            # Calculate average time between peaks
            peak_intervals = np.diff(peaks) / s_freq
            if len(peak_intervals) > 0:
                avg_interval = np.mean(peak_intervals)
                peak_freq1 = 1 / avg_interval if avg_interval > 0 else 0

    # Use peak-to-peak frequency if available
    if peak_to_peak_freq == 1 and peak_freq1 != 0:
        peak_freq = peak_freq1

    HFO_si = np.min(np.where(freqs > HFO_FREQ)) if np.any(
        freqs > HFO_FREQ) else len(freqs) - 1
    HFO_ei = np.max(np.where(freqs < MAX_FREQ)) if np.any(
        freqs < MAX_FREQ) else 0
    total_power_cfa = np.sum(myPower[:HFO_si])  # Power in conventional range
    total_power_hfo = np.sum(myPower[HFO_si:])  # Power in HFO range

    if peak_freq >= HFO_FREQ or total_power_hfo >= pwr_thresh * total_power_cfa:
        flag = 1  # True HFO

    if noise_freq_removal == 1:
        for noise_freq in noise_freq_vector:
            if noise_freq - 2 < peak_freq < noise_freq + 2:
                flag = 2  # Not true HFO (too close to noise frequency)
                break

    # Analyze in chunks if data length exceeds FFT length
    if len(data) > FFT_length:
        flag = 0
        f, t, Sxx = spectrogram(
            data, s_freq, window='hamming', nperseg=FFT_length, noverlap=FFT_length // 2)
        peak_power_vec = np.max(Sxx, axis=0)
        peak_freq_vec = f[np.argmax(Sxx, axis=0)]

        HFO_si_spec = np.min(np.where(f > HFO_FREQ)) if np.any(
            f > HFO_FREQ) else len(f) - 1
        total_power_cfa_vec = np.sum(Sxx[:HFO_si_spec, :], axis=0)
        total_power_hfo_vec = np.sum(Sxx[HFO_si_spec:, :], axis=0)

        for i in range(len(t)):
            if peak_freq_vec[i] >= HFO_FREQ or total_power_hfo_vec[i] >= pwr_thresh * total_power_cfa_vec[i]:
                flag = 1  # True HFO

        if noise_freq_removal == 1:
            for noise_freq in noise_freq_vector:
                if np.sum((noise_freq - 2 < peak_freq_vec) & (peak_freq_vec < noise_freq + 2)) > len(peak_freq_vec) / 2:
                    flag = 2  # Not true HFO (too close to noise frequency)
                    break

    return flag, duration, peak_power, total_power_hfo, peak_freq, my_amp, max_freq


def calculate_hfo_characteristics(hfo_event, raw_data, sampling_rate):
    """Simplified wrapper for backward compatibility"""
    start_sample = int(hfo_event['start_time'] * sampling_rate)
    end_sample = int(hfo_event['end_time'] * sampling_rate)

    hfo_segment = raw_data[start_sample:end_sample]

    characteristics = {
        'duration_ms': hfo_event['duration_ms'],
        'peak_frequency': hfo_event['mean_frequency'],
        'max_amplitude': hfo_event['max_amplitude'],
        'num_peaks': hfo_event['num_peaks'],
        'energy': np.sum(hfo_segment ** 2),
        'power': np.mean(hfo_segment ** 2)
    }

    return characteristics
