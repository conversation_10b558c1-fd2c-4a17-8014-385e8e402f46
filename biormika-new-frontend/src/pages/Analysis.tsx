import { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { PageLayout } from '../components/PageLayout'
import { PageHeader } from '../components/PageHeader'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { PrimaryButton } from '../components/PrimaryButton'
import { useAppSelector } from '../store'
import { useAnalysis } from '../hooks/useAnalysis'
import { brandColors } from '../constants/colors'
import { CheckCircle, XCircle, Download } from 'lucide-react'

export default function Analysis() {
  const { fileId } = useParams<{ fileId: string }>()
  const navigate = useNavigate()
  const { files } = useAppSelector(state => state.files)
  const { globalSettings, fileSpecificSettings } = useAppSelector(state => state.analysisSettings)
  const { 
    isAnalyzing, 
    currentAnalysis, 
    startAnalysis, 
    downloadReport,
    analysisResults,
    error 
  } = useAnalysis({ autoFetchResults: true })
  
  const [analysisStarted, setAnalysisStarted] = useState(false)
  
  const currentFile = files.find(f => f.id === fileId)
  const settings = fileId ? (fileSpecificSettings[fileId] || globalSettings) : globalSettings
  
  useEffect(() => {
    if (fileId && !analysisStarted && !isAnalyzing && currentFile?.status === 'VALID') {
      setAnalysisStarted(true)
      startAnalysis(fileId)
    }
  }, [fileId, analysisStarted, isAnalyzing, currentFile, startAnalysis])
  
  const handleDownloadReport = async () => {
    if (currentAnalysis?.analysisId) {
      await downloadReport(currentAnalysis.analysisId)
    }
  }
  
  const getProgressSteps = () => {
    const steps = [
      { name: 'File validation', status: 'completed' },
      { name: 'Running HFO detection algorithm', status: 'pending' },
      { name: 'Generating analysis report', status: 'pending' },
      { name: 'Preparing results for download', status: 'pending' }
    ]
    
    if (currentAnalysis) {
      const progress = currentAnalysis.progress
      if (progress >= 25) steps[1].status = 'completed'
      if (progress >= 50) steps[2].status = 'completed'
      if (progress >= 75) steps[3].status = 'completed'
      
      if (progress > 0 && progress < 100) {
        const activeStep = Math.floor(progress / 25)
        if (activeStep < 4 && steps[activeStep].status !== 'completed') {
          steps[activeStep].status = 'active'
        }
      }
    }
    
    if (error || currentAnalysis?.status === 'failed') {
      const activeIndex = steps.findIndex(s => s.status === 'active')
      if (activeIndex >= 0) {
        steps[activeIndex].status = 'error'
      }
    }
    
    return steps
  }

  return (
    <PageLayout>
      <div className="w-full max-w-4xl mx-auto">
        <PageHeader 
          title={currentAnalysis?.status === 'completed' ? "Analysis Complete" : "Analysis in Progress"}
          subtitle={currentAnalysis?.status === 'completed' 
            ? "Your EDF file has been successfully analyzed. You can now download the results."
            : "Processing your EDF file with the configured parameters. This may take a few minutes."
          }
          variant={currentAnalysis?.status === 'completed' ? "default" : "processing"}
        >
          {currentAnalysis?.status !== 'completed' && <LoadingSpinner />}
        </PageHeader>

        {/* File Information */}
        <div className="bg-white rounded-lg p-6 mb-6 border" style={{ borderColor: brandColors.border.light }}>
          <h3 className="text-lg font-semibold mb-4" style={{ color: brandColors.text.primary }}>
            File Being Analyzed
          </h3>
          {currentFile && (
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <p className="font-medium" style={{ color: brandColors.text.primary }}>
                  {currentFile.name}
                </p>
                <p className="text-sm" style={{ color: brandColors.text.secondary }}>
                  Size: {(currentFile.size / (1024 * 1024)).toFixed(2)} MB
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm" style={{ color: brandColors.text.secondary }}>
                  Status: {currentAnalysis?.status || 'Initializing'}
                </p>
                <p className="text-sm" style={{ color: brandColors.text.secondary }}>
                  {currentAnalysis?.startedAt && `Started: ${new Date(currentAnalysis.startedAt).toLocaleTimeString()}`}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Analysis Parameters Summary */}
        {settings && (
          <div className="bg-white rounded-lg p-6 mb-6 border" style={{ borderColor: brandColors.border.light }}>
            <h3 className="text-lg font-semibold mb-4" style={{ color: brandColors.text.primary }}>
              Analysis Parameters
            </h3>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2" style={{ color: brandColors.text.secondary }}>
                  Threshold Settings
                </h4>
                <ul className="text-sm space-y-1" style={{ color: brandColors.text.muted }}>
                  <li>Amplitude 1: {settings.thresholdSettings.amplitude1}</li>
                  <li>Amplitude 2: {settings.thresholdSettings.amplitude2}</li>
                  <li>Peaks 1: {settings.thresholdSettings.peaks1}</li>
                  <li>Peaks 2: {settings.thresholdSettings.peaks2}</li>
                  <li>Duration: {settings.thresholdSettings.duration} ms</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2" style={{ color: brandColors.text.secondary }}>
                  Frequency Filters
                </h4>
                <ul className="text-sm space-y-1" style={{ color: brandColors.text.muted }}>
                  <li>Low Cutoff: {settings.frequencyFilterSettings.lowCutoffFilter} Hz</li>
                  <li>High Cutoff: {settings.frequencyFilterSettings.highCutoffFilter} Hz</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2" style={{ color: brandColors.text.secondary }}>
                  Synchronization
                </h4>
                <ul className="text-sm space-y-1" style={{ color: brandColors.text.muted }}>
                  <li>Temporal Sync: {settings.synchronizationSettings.temporalSync} ms</li>
                  <li>Spatial Sync: {settings.synchronizationSettings.spatialSync} ms</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2" style={{ color: brandColors.text.secondary }}>
                  Montage Selection
                </h4>
                <ul className="text-sm space-y-1" style={{ color: brandColors.text.muted }}>
                  <li>Bipolar: {settings.montageSelection.bipolar ? 'Yes' : 'No'}</li>
                  <li>Average: {settings.montageSelection.average ? 'Yes' : 'No'}</li>
                  <li>Referential: {settings.montageSelection.referential ? 'Yes' : 'No'}</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Progress Indicator */}
        <div className="bg-white rounded-lg p-6 border" style={{ borderColor: brandColors.border.light }}>
          <h3 className="text-lg font-semibold mb-4" style={{ color: brandColors.text.primary }}>
            Processing Status
          </h3>
          <div className="space-y-3">
            {getProgressSteps().map((step, index) => (
              <div key={index} className="flex items-center gap-3">
                {step.status === 'completed' && (
                  <CheckCircle className="w-5 h-5" style={{ color: brandColors.success }} />
                )}
                {step.status === 'active' && <LoadingSpinner />}
                {step.status === 'pending' && (
                  <div className="w-4 h-4 rounded-full border-2" style={{ borderColor: brandColors.border.medium }}></div>
                )}
                {step.status === 'error' && (
                  <XCircle className="w-5 h-5" style={{ color: brandColors.error }} />
                )}
                <span style={{ 
                  color: step.status === 'completed' ? brandColors.text.primary : 
                         step.status === 'error' ? brandColors.error :
                         brandColors.text.muted 
                }}>
                  {step.name}
                  {step.status === 'active' && currentAnalysis?.currentStep && (
                    <span className="ml-2 text-sm">({currentAnalysis.progress}%)</span>
                  )}
                </span>
              </div>
            ))}
          </div>
          
          {currentAnalysis?.progress !== undefined && (
            <div className="mt-6">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${currentAnalysis.progress}%`,
                    backgroundColor: error ? brandColors.error : brandColors.primary
                  }}
                />
              </div>
            </div>
          )}
          
          {error && (
            <div className="mt-4 p-4 rounded-lg" style={{ backgroundColor: `${brandColors.error}10`, color: brandColors.error }}>
              <p className="font-medium">Analysis Error</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
          )}
        </div>
        
        {/* Results Section */}
        {currentAnalysis?.status === 'completed' && analysisResults.length > 0 && (
          <div className="bg-white rounded-lg p-6 mt-6 border" style={{ borderColor: brandColors.border.light }}>
            <h3 className="text-lg font-semibold mb-4" style={{ color: brandColors.text.primary }}>
              Analysis Results
            </h3>
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="p-4 rounded-lg" style={{ backgroundColor: brandColors.background.light }}>
                <p className="text-sm" style={{ color: brandColors.text.secondary }}>Total HFOs Detected</p>
                <p className="text-2xl font-bold mt-1" style={{ color: brandColors.primary }}>
                  {analysisResults[0]?.results?.statistics?.totalHFOs || 0}
                </p>
              </div>
              <div className="p-4 rounded-lg" style={{ backgroundColor: brandColors.background.light }}>
                <p className="text-sm" style={{ color: brandColors.text.secondary }}>Average Rate</p>
                <p className="text-2xl font-bold mt-1" style={{ color: brandColors.primary }}>
                  {analysisResults[0]?.results?.statistics?.averageRate?.toFixed(2) || 0} HFOs/min
                </p>
              </div>
            </div>
            
            <div className="flex gap-3">
              <PrimaryButton 
                onClick={handleDownloadReport}
                icon={<Download className="w-4 h-4 mr-2" />}
              >
                Download Report (PDF)
              </PrimaryButton>
              <button
                onClick={() => navigate('/dashboard')}
                className="px-6 py-3 rounded-lg font-medium border transition-colors"
                style={{ 
                  borderColor: brandColors.border.medium,
                  color: brandColors.text.secondary
                }}
              >
                Return to Dashboard
              </button>
            </div>
          </div>
        )}
        
        {/* Action Buttons for Failed Analysis */}
        {(error || currentAnalysis?.status === 'failed') && (
          <div className="flex gap-3 mt-6 justify-center">
            <PrimaryButton onClick={() => navigate('/dashboard')}>
              Return to Dashboard
            </PrimaryButton>
            <button
              onClick={() => {
                setAnalysisStarted(false)
                if (fileId) startAnalysis(fileId)
              }}
              className="px-6 py-3 rounded-lg font-medium border transition-colors"
              style={{ 
                borderColor: brandColors.primary,
                color: brandColors.primary
              }}
            >
              Retry Analysis
            </button>
          </div>
        )}
      </div>
    </PageLayout>
  )
}