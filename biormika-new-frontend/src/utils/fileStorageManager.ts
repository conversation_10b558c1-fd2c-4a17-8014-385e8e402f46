/**
 * Global File storage manager to ensure File objects are accessible
 * across all instances of useFileUpload hook
 */
class FileStorageManager {
  private storage: Map<string, File>
  private refCount: number

  constructor() {
    this.storage = new Map()
    this.refCount = 0
  }

  /**
   * Register a component using the file storage
   */
  addRef(): void {
    this.refCount++
  }

  /**
   * Unregister a component using the file storage
   */
  removeRef(): void {
    this.refCount = Math.max(0, this.refCount - 1)

    // Only clear storage when no components are using it
    if (this.refCount === 0) {
      this.storage.clear()
    }
  }

  /**
   * Add a File object to storage
   */
  set(id: string, file: File): void {
    this.storage.set(id, file)
  }

  /**
   * Get a File object from storage
   */
  get(id: string): File | undefined {
    return this.storage.get(id)
  }

  /**
   * Check if a File object exists in storage
   */
  has(id: string): boolean {
    return this.storage.has(id)
  }

  /**
   * Remove a File object from storage
   */
  delete(id: string): boolean {
    console.log(`[FileStorageManager] Deleting file with ID: ${id}`)
    return this.storage.delete(id)
  }

  /**
   * Clear all File objects from storage (force clear regardless of ref count)
   */
  clear(): void {
    console.log(`[FileStorageManager] Force clearing all files (count: ${this.storage.size})`)
    this.storage.clear()
  }

  /**
   * Get current reference count
   */
  getRefCount(): number {
    return this.refCount
  }

  /**
   * Get all stored file IDs
   */
  getIds(): string[] {
    return Array.from(this.storage.keys())
  }

  /**
   * Get the number of stored files
   */
  size(): number {
    return this.storage.size
  }

  /**
   * Debug helper to log current storage state
   */
  logState(context: string): void {
    console.log(
      `[FileStorageManager - ${context}] File count: ${
        this.storage.size
      }, IDs: ${this.getIds().join(', ')}`
    )
  }
}

// Create a singleton instance
export const fileStorageManager = new FileStorageManager()
