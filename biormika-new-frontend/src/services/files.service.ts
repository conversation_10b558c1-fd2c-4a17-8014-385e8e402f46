import axios from 'axios'
import { apiClient } from '../lib/axios'
import { API_ENDPOINTS, FILE_UPLOAD_CONFIG, API_BASE_URL } from '../constants/api'
import { store } from '../store'
import { MultipartUploadService } from './multipartUpload.service'
import type {
  ApiResponse,
  FileUploadInitRequest,
  FileUploadInitResponse,
  FileValidateResponse,
} from '../types/api'

export interface FileListItem {
  id: string
  name: string
  size: number
  status: 'pending' | 'uploading' | 'validating' | 'valid' | 'invalid' | 'error'
  uploadedAt: string
  validatedAt?: string
  metadata?: {
    channels: string[]
    duration: number
    sampleRate: number
  }
  error?: string
}

export interface UploadProgress {
  fileId: string
  progress: number
  uploaded: number
  total: number
}

type ProgressCallback = (progress: UploadProgress) => void

class FilesService {
  private uploadAbortControllers: Map<string, AbortController> = new Map()
  private multipartUploadService = new MultipartUploadService()

  async initUpload(file: File): Promise<FileUploadInitResponse> {
    const request: FileUploadInitRequest = {
      fileName: file.name,
      fileSize: file.size,
      contentType: file.type || 'application/octet-stream',
    }

    const response = await apiClient.post<ApiResponse<FileUploadInitResponse>>(
      API_ENDPOINTS.files.initUpload,
      request,
      { showErrorToast: true }
    )

    return response.data.data
  }

  async uploadToS3(
    file: File,
    uploadUrl: string,
    uploadFields?: Record<string, string>,
    onProgress?: ProgressCallback,
    fileId?: string
  ): Promise<void> {
    const formData = new FormData()

    if (uploadFields) {
      Object.entries(uploadFields).forEach(([key, value]) => {
        formData.append(key, value)
      })
    }

    formData.append('file', file)

    const abortController = new AbortController()
    if (fileId) {
      this.uploadAbortControllers.set(fileId, abortController)
    }

    try {
      console.log('Uploading file to S3:', { uploadUrl, fields: uploadFields, fileSize: file.size })

      await axios.post(uploadUrl, formData, {
        signal: abortController.signal,
        timeout: 300000, // 5 minutes timeout for large files
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total && fileId) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onProgress({
              fileId,
              progress,
              uploaded: progressEvent.loaded,
              total: progressEvent.total,
            })
          }
        },
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      console.log('S3 upload completed successfully')
    } catch (error) {
      console.error('S3 upload failed:', error)
      throw error
    } finally {
      if (fileId) {
        this.uploadAbortControllers.delete(fileId)
      }
    }
  }

  async validateFile(fileId: string): Promise<FileValidateResponse> {
    const response = await apiClient.post<ApiResponse<FileValidateResponse>>(
      API_ENDPOINTS.files.validate(fileId),
      {},
      { showErrorToast: true }
    )

    return response.data.data
  }

  async listFiles(page = 1, limit = 20): Promise<{ files: FileListItem[]; total: number }> {
    const response = await apiClient.get<ApiResponse<{ files: FileListItem[]; total: number }>>(
      `${API_ENDPOINTS.files.list}?page=${page}&limit=${limit}`,
      {
        showErrorToast: true,
      }
    )

    return response.data.data
  }

  async deleteFile(fileId: string): Promise<void> {
    await apiClient.delete(API_ENDPOINTS.files.delete(fileId), {
      showSuccessToast: true,
      successMessage: 'File deleted successfully',
    })
  }

  async downloadFile(fileId: string, fileName: string): Promise<void> {
    const state = store.getState()
    const token = state.auth.token

    const response = await axios.get(`${API_BASE_URL}${API_ENDPOINTS.files.download(fileId)}`, {
      responseType: 'blob',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
  }

  async uploadFile(
    file: File,
    onProgress?: ProgressCallback
  ): Promise<{ fileId: string; status: FileValidateResponse['status'] }> {
    try {
      console.log('Starting file upload:', { fileName: file.name, fileSize: file.size })

      if (file.size > FILE_UPLOAD_CONFIG.maxSize) {
        throw new Error(
          `File size exceeds maximum allowed size of ${FILE_UPLOAD_CONFIG.maxSize / 1024 / 1024}MB`
        )
      }

      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`
      if (!FILE_UPLOAD_CONFIG.allowedTypes.includes(fileExtension)) {
        throw new Error(
          `File type ${fileExtension} is not allowed. Allowed types: ${FILE_UPLOAD_CONFIG.allowedTypes.join(
            ', '
          )}`
        )
      }

      // Use multipart upload for files larger than threshold
      if (file.size > FILE_UPLOAD_CONFIG.multipartThreshold) {
        console.log('Using multipart upload for large file')
        return await this.uploadFileMultipart(file, onProgress)
      }

      console.log('Using single upload for small file')
      const { fileId, uploadUrl, uploadFields } = await this.initUpload(file)
      console.log('Upload initialized:', { fileId, uploadUrl })

      console.log('Uploading to S3...')
      await this.uploadToS3(file, uploadUrl, uploadFields, onProgress, fileId)
      console.log('S3 upload complete, validating file...')

      const validationResult = await this.validateFile(fileId)
      console.log('Validation result:', validationResult)

      return {
        fileId,
        status: validationResult.status,
      }
    } catch (error) {
      console.error('Upload error:', error)
      if (axios.isCancel(error)) {
        throw new Error('Upload cancelled')
      }
      throw error
    }
  }

  private async uploadFileMultipart(
    file: File,
    onProgress?: ProgressCallback
  ): Promise<{ fileId: string; status: FileValidateResponse['status'] }> {
    const fileId = Math.random().toString(36).substring(2, 15)

    try {
      await this.multipartUploadService.uploadFile({
        fileId,
        file,
        chunkSize: FILE_UPLOAD_CONFIG.chunkSize,
        maxConcurrentUploads: FILE_UPLOAD_CONFIG.maxConcurrentUploads,
        onProgress: (progress) => {
          if (onProgress) {
            onProgress({
              fileId,
              progress: progress.totalProgress,
              uploaded: progress.uploadedBytes,
              total: progress.totalBytes,
            })
          }
        },
        onChunkComplete: (chunkNumber, totalChunks) => {
          console.log(`Chunk ${chunkNumber}/${totalChunks} completed`)
        },
        onError: (error, chunkNumber) => {
          console.error(`Error uploading chunk ${chunkNumber}:`, error)
        },
      })

      console.log('Multipart upload complete, validating file...')
      const validationResult = await this.validateFile(fileId)
      console.log('Validation result:', validationResult)

      return {
        fileId,
        status: validationResult.status,
      }
    } catch (error) {
      console.error('Multipart upload error:', error)
      throw error
    }
  }

  cancelUpload(fileId: string): void {
    // Try to cancel regular upload
    const controller = this.uploadAbortControllers.get(fileId)
    if (controller) {
      controller.abort()
      this.uploadAbortControllers.delete(fileId)
    }
    
    // Also abort multipart upload if in progress
    this.multipartUploadService.abort()
  }

  isValidFileType(file: File): boolean {
    const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`
    return FILE_UPLOAD_CONFIG.allowedTypes.includes(fileExtension)
  }

  isValidFileSize(file: File): boolean {
    return file.size <= FILE_UPLOAD_CONFIG.maxSize
  }

  getFileValidationError(file: File): string | null {
    if (!this.isValidFileType(file)) {
      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`
      return `File type ${fileExtension} is not allowed. Allowed types: ${FILE_UPLOAD_CONFIG.allowedTypes.join(
        ', '
      )}`
    }

    if (!this.isValidFileSize(file)) {
      return `File size exceeds maximum allowed size of ${
        FILE_UPLOAD_CONFIG.maxSize / 1024 / 1024
      }MB`
    }

    return null
  }
}

export const filesService = new FilesService()
