import { Button } from 'antd'
import type { ReactNode } from 'react'
import { brandColors } from '../constants/colors'

interface SecondaryButtonProps {
  children: ReactNode
  loading?: boolean
  htmlType?: 'button' | 'submit' | 'reset'
  onClick?: () => void
  className?: string
  fullWidth?: boolean
  disabled?: boolean
}

export function SecondaryButton({ 
  children, 
  loading = false, 
  htmlType = 'button',
  onClick,
  className = '',
  fullWidth = false,
  disabled = false
}: SecondaryButtonProps) {
  return (
    <Button
      htmlType={htmlType}
      loading={loading}
      onClick={onClick}
      disabled={disabled}
      style={{
        backgroundColor: 'transparent',
        borderColor: brandColors.border.medium,
        color: brandColors.text.secondary,
      }}
      className={`
        ${fullWidth ? 'w-full' : ''} 
        h-12 hover:bg-gray-50 hover:border-gray-400 hover:text-gray-700
        rounded-lg font-medium text-base 
        ${className}
      `}
    >
      {children}
    </Button>
  )
}