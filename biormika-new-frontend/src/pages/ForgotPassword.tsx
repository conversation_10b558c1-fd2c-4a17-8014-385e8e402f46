import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Button, Form } from 'antd'
import { AuthLayout } from '../components/AuthLayout'
import { AuthCard } from '../components/AuthCard'
import { BrandHeader } from '../components/BrandHeader'
import { FormField } from '../components/FormField'
import { PrimaryButton } from '../components/PrimaryButton'
import { useAuth } from '../hooks/useAuth'

interface ForgotPasswordForm {
  email: string
}

export default function ForgotPassword() {
  const [form] = Form.useForm()
  const [submitted, setSubmitted] = useState(false)
  const { isSubmitting, error, forgotPassword, clearError } = useAuth()

  React.useEffect(() => {
    clearError()
  }, [clearError])

  const handleSubmit = async (values: ForgotPasswordForm) => {
    const success = await forgotPassword(values.email)
    if (success) {
      setSubmitted(true)
    }
  }

  return (
    <AuthLayout>
      <AuthCard>
        <BrandHeader />

        <div className="mb-6">
          <h2 className="text-xl font-semibold text-text-primary text-center">Reset Password</h2>
          {submitted ? (
            <p className="text-text-secondary text-sm text-center mt-2">
              Password reset instructions have been sent to your email.
            </p>
          ) : (
            <p className="text-text-secondary text-sm text-center mt-2">
              Enter your email and we'll send you instructions to reset your password.
            </p>
          )}
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="space-y-4"
        >
          <FormField
            label="Email"
            name="email"
            type="email"
            placeholder="<EMAIL>"
            required
          />

          {error && (
            <div className="text-error text-sm text-center mt-2">
              {error}
            </div>
          )}

          <div className="pt-4">
            <Form.Item>
              <PrimaryButton htmlType="submit" loading={isSubmitting} disabled={submitted}>
                {submitted ? 'Email Sent' : 'Reset Password'}
              </PrimaryButton>
            </Form.Item>
          </div>

          <div className="pt-2">
            <Link to="/login">
              <Button
                className="w-full h-12 border-border-light hover:border-border-medium rounded-lg font-medium text-text-secondary"
              >
                Back to Login
              </Button>
            </Link>
          </div>
        </Form>

        <div className="mt-8 text-center">
          <span className="text-text-secondary text-sm">
            Don't have an account?{' '}
            <Link 
              to="/signup" 
              className="text-brand-primary hover:text-brand-primary-dark font-medium"
            >
              Sign up
            </Link>
          </span>
        </div>
      </AuthCard>
    </AuthLayout>
  )
}