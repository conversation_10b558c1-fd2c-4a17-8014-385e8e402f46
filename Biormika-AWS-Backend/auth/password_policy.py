import re

def validate_password(password):
    if not password:
        return False, "Password is required"
    
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    
    if not re.search(r'[0-9]', password):
        return False, "Password must contain at least one number"
    
    return True, None

def get_password_requirements():
    return {
        "minLength": 8,
        "requireUppercase": True,
        "requireLowercase": True,
        "requireNumbers": True,
        "requireSymbols": False
    }