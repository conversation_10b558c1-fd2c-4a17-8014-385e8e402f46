import axios from 'axios'
import { apiClient } from '../lib/axios'
import { API_ENDPOINTS } from '../constants/api'

export interface MultipartUploadConfig {
  fileId: string
  file: File
  chunkSize?: number
  maxConcurrentUploads?: number
  onProgress?: (progress: MultipartUploadProgress) => void
  onChunkComplete?: (chunkNumber: number, totalChunks: number) => void
  onError?: (error: Error, chunkNumber?: number) => void
}

export interface MultipartUploadProgress {
  fileId: string
  totalProgress: number
  uploadedBytes: number
  totalBytes: number
  chunksCompleted: number
  totalChunks: number
  currentChunkProgress: Record<number, number>
}

export interface MultipartInitResponse {
  fileId: string
  uploadId: string
  s3Key: string
  bucket: string
}

export interface PartUploadUrl {
  partNumber: number
  url: string
}

export interface CompletedPart {
  partNumber: number
  eTag: string
}

const DEFAULT_CHUNK_SIZE = 10 * 1024 * 1024 // 10MB
const DEFAULT_MAX_CONCURRENT = 3
const MAX_RETRY_ATTEMPTS = 3
const RETRY_DELAY_BASE = 1000 // 1 second

export class MultipartUploadService {
  private abortController: AbortController | null = null

  async uploadFile(config: MultipartUploadConfig): Promise<void> {
    const {
      fileId,
      file,
      chunkSize = DEFAULT_CHUNK_SIZE,
      maxConcurrentUploads = DEFAULT_MAX_CONCURRENT,
      onProgress,
      onChunkComplete,
      onError
    } = config

    this.abortController = new AbortController()

    try {
      // Calculate chunks
      const totalChunks = Math.ceil(file.size / chunkSize)
      const chunks = this.createChunks(file, chunkSize)

      // Initialize multipart upload
      const initResponse = await this.initializeMultipartUpload(file)
      
      // Get presigned URLs for all parts
      const uploadUrls = await this.getUploadUrls(
        initResponse.uploadId,
        initResponse.s3Key,
        totalChunks
      )

      // Track progress
      const progress: MultipartUploadProgress = {
        fileId,
        totalProgress: 0,
        uploadedBytes: 0,
        totalBytes: file.size,
        chunksCompleted: 0,
        totalChunks,
        currentChunkProgress: {}
      }

      // Upload chunks with concurrency control
      const completedParts = await this.uploadChunksWithConcurrency(
        chunks,
        uploadUrls,
        maxConcurrentUploads,
        (chunkNumber, chunkProgress) => {
          progress.currentChunkProgress[chunkNumber] = chunkProgress
          
          // Calculate total progress
          const baseProgress = progress.chunksCompleted * chunkSize
          const currentChunksProgress = Object.values(progress.currentChunkProgress)
            .reduce((sum, p) => sum + p, 0)
          
          progress.uploadedBytes = Math.min(baseProgress + currentChunksProgress, file.size)
          progress.totalProgress = Math.round((progress.uploadedBytes / file.size) * 100)
          
          onProgress?.(progress)
        },
        (chunkNumber) => {
          progress.chunksCompleted++
          delete progress.currentChunkProgress[chunkNumber]
          onChunkComplete?.(chunkNumber, totalChunks)
        },
        onError
      )

      // Complete multipart upload
      await this.completeMultipartUpload(
        fileId,
        initResponse.uploadId,
        initResponse.s3Key,
        completedParts
      )

      // Final progress update
      if (onProgress) {
        onProgress({
          ...progress,
          totalProgress: 100,
          uploadedBytes: file.size,
          chunksCompleted: totalChunks
        })
      }

    } catch (error) {
      if (axios.isCancel(error)) {
        throw new Error('Upload cancelled')
      }
      throw error
    } finally {
      this.abortController = null
    }
  }

  abort(): void {
    this.abortController?.abort()
  }

  private createChunks(file: File, chunkSize: number): Blob[] {
    const chunks: Blob[] = []
    let offset = 0

    while (offset < file.size) {
      const end = Math.min(offset + chunkSize, file.size)
      chunks.push(file.slice(offset, end))
      offset = end
    }

    return chunks
  }

  private async initializeMultipartUpload(file: File): Promise<MultipartInitResponse> {
    const response = await apiClient.post(
      API_ENDPOINTS.files.multipart.init,
      {
        fileName: file.name,
        fileSize: file.size,
        contentType: file.type || 'application/octet-stream'
      }
    )

    return response.data.data
  }

  private async getUploadUrls(
    uploadId: string,
    s3Key: string,
    parts: number
  ): Promise<PartUploadUrl[]> {
    const response = await apiClient.post(
      API_ENDPOINTS.files.multipart.urls,
      {
        uploadId,
        s3Key,
        parts
      }
    )

    return response.data.data.urls
  }

  private async uploadChunksWithConcurrency(
    chunks: Blob[],
    uploadUrls: PartUploadUrl[],
    maxConcurrent: number,
    onChunkProgress: (chunkNumber: number, progress: number) => void,
    onChunkComplete: (chunkNumber: number) => void,
    onError?: (error: Error, chunkNumber?: number) => void
  ): Promise<CompletedPart[]> {
    const completedParts: CompletedPart[] = []
    const uploadQueue = [...uploadUrls]
    const activeUploads = new Map<number, Promise<CompletedPart>>()

    while (uploadQueue.length > 0 || activeUploads.size > 0) {
      // Start new uploads up to max concurrent
      while (uploadQueue.length > 0 && activeUploads.size < maxConcurrent) {
        const partUpload = uploadQueue.shift()!
        const chunkIndex = partUpload.partNumber - 1
        const chunk = chunks[chunkIndex]

        const uploadPromise = this.uploadChunkWithRetry(
          chunk,
          partUpload,
          (progress) => onChunkProgress(partUpload.partNumber, progress),
          onError
        ).then((completedPart) => {
          onChunkComplete(partUpload.partNumber)
          return completedPart
        })

        activeUploads.set(partUpload.partNumber, uploadPromise)
      }

      // Wait for at least one upload to complete
      if (activeUploads.size > 0) {
        const completed = await Promise.race(activeUploads.values())
        completedParts.push(completed)
        activeUploads.delete(completed.partNumber)
      }
    }

    return completedParts.sort((a, b) => a.partNumber - b.partNumber)
  }

  private async uploadChunkWithRetry(
    chunk: Blob,
    partUpload: PartUploadUrl,
    onProgress: (progress: number) => void,
    onError?: (error: Error, chunkNumber?: number) => void,
    attempt = 1
  ): Promise<CompletedPart> {
    try {
      const response = await axios.put(partUpload.url, chunk, {
        signal: this.abortController?.signal,
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const progress = progressEvent.loaded
            onProgress(progress)
          }
        },
        headers: {
          'Content-Type': 'application/octet-stream'
        }
      })

      const eTag = response.headers['etag'] || response.headers['ETag']
      if (!eTag) {
        throw new Error(`No ETag received for part ${partUpload.partNumber}`)
      }

      return {
        partNumber: partUpload.partNumber,
        eTag: eTag.replace(/"/g, '') // Remove quotes from ETag
      }
    } catch (error) {
      if (axios.isCancel(error)) {
        throw error
      }

      if (attempt < MAX_RETRY_ATTEMPTS) {
        const delay = RETRY_DELAY_BASE * Math.pow(2, attempt - 1)
        await new Promise(resolve => setTimeout(resolve, delay))
        return this.uploadChunkWithRetry(chunk, partUpload, onProgress, onError, attempt + 1)
      }

      const uploadError = new Error(
        `Failed to upload part ${partUpload.partNumber} after ${MAX_RETRY_ATTEMPTS} attempts`
      )
      onError?.(uploadError, partUpload.partNumber)
      throw uploadError
    }
  }

  private async completeMultipartUpload(
    fileId: string,
    uploadId: string,
    s3Key: string,
    parts: CompletedPart[]
  ): Promise<void> {
    await apiClient.post(
      API_ENDPOINTS.files.multipart.complete,
      {
        fileId,
        uploadId,
        s3Key,
        parts: parts.map(part => ({
          partNumber: part.partNumber,
          eTag: part.eTag
        }))
      }
    )
  }

  async abortMultipartUpload(
    fileId: string,
    uploadId: string,
    s3Key: string
  ): Promise<void> {
    try {
      await apiClient.post(
        API_ENDPOINTS.files.multipart.abort,
        {
          fileId,
          uploadId,
          s3Key
        }
      )
    } catch (error) {
      console.error('Failed to abort multipart upload:', error)
    }
  }
}