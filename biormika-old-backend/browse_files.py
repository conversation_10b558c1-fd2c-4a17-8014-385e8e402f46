import numpy as np
import re

def browse_edf(fname, assign_to_variables=False, target_signals=None):
    """
    Direct binary reading of an EDF file in Python, equivalent to MATLAB's edfread function.
    
    Parameters:
    -----------
    fname : str
        Path to the EDF file.
    assign_to_variables : bool, optional
        If True, assigns channels to separate variables (not fully supported in Python as in MATLAB).
    target_signals : list or str, optional
        Channels to extract. If None, all channels are read.

    Returns:
    --------
    hdr : dict
        Header information similar to the MATLAB structure.
    record : numpy.ndarray
        Data matrix with shape (n_channels, n_samples).
    """
    # Open the EDF file
    with open(fname, 'rb') as fid:
        # Read header
        hdr = {}
        hdr['ver'] = int(fid.read(8).decode('ascii').strip())  # 8 ASCII characters
        hdr['patientID'] = fid.read(80).decode('ascii').strip()  # 80 ASCII characters
        hdr['recordID'] = fid.read(80).decode('ascii').strip()  # 80 ASCII characters
        hdr['startdate'] = fid.read(8).decode('ascii').strip()  # 8 ASCII characters
        hdr['starttime'] = fid.read(8).decode('ascii').strip()  # 8 ASCII characters
        hdr['bytes'] = int(fid.read(8).decode('ascii').strip())  # 8 ASCII characters
        fid.read(44)  # Reserved (44 bytes)
        hdr['records'] = int(fid.read(8).decode('ascii').strip())  # 8 ASCII characters
        hdr['duration'] = float(fid.read(8).decode('ascii').strip())  # 8 ASCII characters
        hdr['ns'] = int(fid.read(4).decode('ascii').strip())  # 4 ASCII characters
        
        # Read signal-specific information
        hdr['label'] = [re.sub(r'\W+$', '', fid.read(16).decode('ascii').strip()) for _ in range(hdr['ns'])]
        hdr['transducer'] = [fid.read(80).decode('ascii').strip() for _ in range(hdr['ns'])]
        hdr['units'] = [fid.read(8).decode('ascii').strip() for _ in range(hdr['ns'])]
        hdr['physicalMin'] = np.array([float(fid.read(8).decode('ascii').strip()) for _ in range(hdr['ns'])])
        hdr['physicalMax'] = np.array([float(fid.read(8).decode('ascii').strip()) for _ in range(hdr['ns'])])
        hdr['digitalMin'] = np.array([int(fid.read(8).decode('ascii').strip()) for _ in range(hdr['ns'])])
        hdr['digitalMax'] = np.array([int(fid.read(8).decode('ascii').strip()) for _ in range(hdr['ns'])])
        hdr['prefilter'] = [fid.read(80).decode('ascii').strip() for _ in range(hdr['ns'])]
        hdr['samples'] = np.array([int(fid.read(8).decode('ascii').strip()) for _ in range(hdr['ns'])])
        for _ in range(hdr['ns']):
            fid.read(32)  # Reserved (32 bytes)
        
        hdr['frequency'] = hdr['samples'] / hdr['duration']

        # Handle target signals selection
        if target_signals is None:
            target_signals = list(range(hdr['ns']))
        elif isinstance(target_signals, (str, list)):
            if isinstance(target_signals, str):
                target_signals = [target_signals]
            target_signals = [i for i, label in enumerate(hdr['label']) if label in target_signals]
            if not target_signals:
                raise ValueError("EDFREAD: The requested signals were not detected.")
        else:
            raise ValueError("EDFREAD: Invalid targetSignals parameter.")

        hdr['label'] = [hdr['label'][i] for i in target_signals]
        hdr['units'] = [hdr['units'][i] for i in target_signals]
        hdr['physicalMin'] = hdr['physicalMin'][target_signals]
        hdr['physicalMax'] = hdr['physicalMax'][target_signals]
        hdr['digitalMin'] = hdr['digitalMin'][target_signals]
        hdr['digitalMax'] = hdr['digitalMax'][target_signals]
        hdr['prefilter'] = [hdr['prefilter'][i] for i in target_signals]
        hdr['transducer'] = [hdr['transducer'][i] for i in target_signals]
        hdr['samples'] = hdr['samples'][target_signals]

        # Calculate scaling factors
        scalefac = (hdr['physicalMax'] - hdr['physicalMin']) / (hdr['digitalMax'] - hdr['digitalMin'])
        dc = hdr['physicalMax'] - scalefac * hdr['digitalMax']

        # # Read and scale data records
        # record = []

        # for _ in range(hdr['records']):
        #     rec = []
        #     for i in range(hdr['ns']):
        #         if i in target_signals:
        #             # Directly use the number of samples for the current channel
        #             samples = hdr['samples'][i]
        #             # Read the data
        #             data = np.fromfile(fid, dtype=np.int16, count=samples)
        #             # Apply scaling using the corresponding index
        #             scale_index = target_signals.index(i)
        #             data = data * scalefac[scale_index] + dc[scale_index]
        #             rec.append(data)
        #         else:
        #             # Skip unwanted channels by moving the file pointer
        #             fid.seek(hdr['samples'][i] * 2, 1)  # Each sample is 2 bytes (int16)
        #     record.append(rec)  # Append the list of channels for this record

        # # Convert record to a NumPy array for easier handling
        # record = np.array(record)  # Shape should be (records, channels, samples)
        # record = record.swapaxes(0, 1).reshape(len(target_signals), -1)  # Reshape to (channels, total_samples)

        # Verify the shape of the resulting array
        # print(f"patient ID {hdr['patientID']}")
        # print(f"length of ns: {hdr['ns']}, record.shape: {record.shape}")
        # print(f"labels: {hdr['label']}")

    return hdr
