import { apiClient } from '../lib/axios'
import { API_ENDPOINTS, OAUTH_CONFIG } from '../constants/api'
import { tokenStorage } from '../utils/token'
import type {
  ApiResponse,
  AuthLoginRequest,
  AuthLoginResponse,
  AuthSignupRequest,
  AuthSignupResponse,
  AuthRefreshRequest,
  AuthRefreshResponse,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  SocialAuthRequest,
} from '../types/api'

class AuthService {
  async login(credentials: AuthLoginRequest): Promise<AuthLoginResponse> {
    const response = await apiClient.post<ApiResponse<AuthLoginResponse>>(
      API_ENDPOINTS.auth.login,
      credentials,
      { showErrorToast: true }
    )
    
    const { user, token, refreshToken } = response.data.data
    
    tokenStorage.setTokens(token, refreshToken)
    
    return { user, token, refreshToken }
  }
  
  async signup(data: AuthSignupRequest): Promise<AuthSignupResponse> {
    const response = await apiClient.post<ApiResponse<AuthSignupResponse>>(
      API_ENDPOINTS.auth.signup,
      data,
      { showErrorToast: true }
    )
    
    const { user, token, refreshToken } = response.data.data
    
    tokenStorage.setTokens(token, refreshToken)
    
    return { user, token, refreshToken }
  }
  
  async refreshToken(refreshToken: string): Promise<AuthRefreshResponse> {
    const response = await apiClient.post<ApiResponse<AuthRefreshResponse>>(
      API_ENDPOINTS.auth.refresh,
      { refreshToken } as AuthRefreshRequest,
      { skipAuthRefresh: true, showErrorToast: false }
    )
    
    const { token, refreshToken: newRefreshToken } = response.data.data
    
    tokenStorage.setTokens(token, newRefreshToken)
    
    return { token, refreshToken: newRefreshToken }
  }
  
  async logout(): Promise<void> {
    try {
      await apiClient.post(
        API_ENDPOINTS.auth.logout,
        {},
        { showErrorToast: false }
      )
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      tokenStorage.clearTokens()
    }
  }
  
  async forgotPassword(email: string): Promise<void> {
    await apiClient.post<ApiResponse<void>>(
      API_ENDPOINTS.auth.forgotPassword,
      { email } as ForgotPasswordRequest,
      { 
        showSuccessToast: true,
        successMessage: 'Password reset instructions sent to your email'
      }
    )
  }
  
  async resetPassword(data: ResetPasswordRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>(
      API_ENDPOINTS.auth.resetPassword,
      data,
      { 
        showSuccessToast: true,
        successMessage: 'Password reset successful. Please login with your new password.'
      }
    )
  }
  
  async socialAuth(provider: 'google' | 'github', accessToken: string): Promise<AuthLoginResponse> {
    const response = await apiClient.post<ApiResponse<AuthLoginResponse>>(
      API_ENDPOINTS.auth.socialAuth,
      { provider, accessToken } as SocialAuthRequest,
      { showErrorToast: true }
    )
    
    const { user, token, refreshToken } = response.data.data
    
    tokenStorage.setTokens(token, refreshToken)
    
    return { user, token, refreshToken }
  }
  
  async validateToken(): Promise<boolean> {
    const token = tokenStorage.getAccessToken()
    
    if (!token || tokenStorage.isTokenExpired(token)) {
      return false
    }
    
    try {
      await apiClient.get('/auth/validate', { 
        showErrorToast: false,
        skipAuthRefresh: false 
      })
      return true
    } catch {
      return false
    }
  }
  
  getStoredTokens() {
    return {
      accessToken: tokenStorage.getAccessToken(),
      refreshToken: tokenStorage.getRefreshToken(),
    }
  }
  
  clearStoredTokens() {
    tokenStorage.clearTokens()
  }
  
  initiateGoogleAuth() {
    const { clientId, redirectUri, scope } = OAUTH_CONFIG.google
    const state = this.generateOAuthState()
    
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope,
      state,
      access_type: 'offline',
      prompt: 'consent',
    })
    
    sessionStorage.setItem('oauth_state', state)
    window.location.href = `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`
  }
  
  initiateGithubAuth() {
    const { clientId, redirectUri, scope } = OAUTH_CONFIG.github
    const state = this.generateOAuthState()
    
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      scope,
      state,
    })
    
    sessionStorage.setItem('oauth_state', state)
    window.location.href = `https://github.com/login/oauth/authorize?${params.toString()}`
  }
  
  async handleOAuthCallback(provider: 'google' | 'github', code: string, state: string): Promise<AuthLoginResponse> {
    const savedState = sessionStorage.getItem('oauth_state')
    
    if (!savedState || savedState !== state) {
      throw new Error('Invalid OAuth state')
    }
    
    sessionStorage.removeItem('oauth_state')
    
    const response = await apiClient.post<ApiResponse<AuthLoginResponse>>(
      API_ENDPOINTS.auth.socialAuth,
      { 
        provider, 
        code,
        redirectUri: provider === 'google' ? OAUTH_CONFIG.google.redirectUri : OAUTH_CONFIG.github.redirectUri
      },
      { showErrorToast: true }
    )
    
    const { user, token, refreshToken } = response.data.data
    
    tokenStorage.setTokens(token, refreshToken)
    
    return { user, token, refreshToken }
  }
  
  private generateOAuthState(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }
}

export const authService = new AuthService()