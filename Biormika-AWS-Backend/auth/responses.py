import json
import os

def create_response(status_code, body):
    allowed_origins = os.environ.get('ALLOWED_ORIGINS', '*').split(',')
    
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': allowed_origins[0] if len(allowed_origins) == 1 else '*',
            'Access-Control-Allow-Credentials': True,
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
        },
        'body': json.dumps(body)
    }

def success_response(data=None, message="Success", status_code=200):
    return create_response(status_code, {
        'success': True,
        'data': data,
        'message': message,
        'statusCode': status_code
    })

def error_response(message, status_code=400, error_code=None):
    body = {
        'success': False,
        'message': message,
        'statusCode': status_code
    }
    
    if error_code:
        body['errorCode'] = error_code
    
    return create_response(status_code, body)