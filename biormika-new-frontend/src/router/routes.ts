export interface RouteConfig {
  path: string
  element: string
  title: string
  requireAuth?: boolean
  guestOnly?: boolean
}

export const routes: RouteConfig[] = [
  {
    path: '/',
    element: 'Dashboard',
    title: 'Dashboard',
    requireAuth: true,
  },
  {
    path: '/configuration',
    element: 'ConfigurationParameters',
    title: 'Configuration Parameters',
    requireAuth: true,
  },
  {
    path: '/analysis',
    element: 'Analysis',
    title: 'Analysis',
    requireAuth: true,
  },
  {
    path: '/login',
    element: 'Login',
    title: 'Login',
    guestOnly: true,
  },
  {
    path: '/signup',
    element: 'SignUp',
    title: 'Sign Up',
    guestOnly: true,
  },
  {
    path: '/forgot-password',
    element: 'ForgotPassword',
    title: 'Forgot Password',
    guestOnly: true,
  },
  {
    path: '/request-demo',
    element: 'RequestDemo',
    title: 'Request Demo',
    guestOnly: true,
  },
  {
    path: '*',
    element: 'NotFound',
    title: 'Page Not Found',
  },
]