from fpdf import FPDF
import boto3
import os
import tempfile
from datetime import datetime
from PIL import Image

s3 = boto3.client('s3')
S3_BUCKET = os.environ.get('S3_BUCKET')


class HFOReport(FPDF):
    def header(self):
        self.set_font('Arial', 'B', 16)
        self.cell(0, 10, 'Biormika HFO Analysis Report', 0, 1, 'C')
        self.ln(5)

    def footer(self):
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')


def generate_pdf_report(hfo_results, visualization_keys, analysis_id, user_id):
    pdf = HFOReport()
    pdf.add_page()

    pdf.set_font('Arial', '', 12)
    pdf.cell(0, 10, f'Analysis ID: {analysis_id}', 0, 1)
    pdf.cell(
        0, 10,
        f'Generated: {datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC")}',
        0, 1
    )
    pdf.ln(10)

    pdf.set_font('Arial', 'B', 14)
    pdf.cell(0, 10, 'Summary', 0, 1)
    pdf.set_font('Arial', '', 12)

    summary_data = [
        ['Total HFOs Detected', str(hfo_results['total_hfos'])],
        ['HFO Rate (events/second)', f"{hfo_results['hfo_rate']:.3f}"],
        ['Analysis Duration (seconds)',
         f"{hfo_results['analysis_duration']:.1f}"],
        ['Number of Channels', str(len(hfo_results['channels']))]
    ]

    if hfo_results.get('synchronized_events'):
        summary_data.append(['Synchronized Event Groups', str(
            len(hfo_results['synchronized_events']))])

    for label, value in summary_data:
        pdf.cell(90, 8, label + ':', 0, 0)
        pdf.cell(0, 8, value, 0, 1)

    pdf.ln(10)
    pdf.set_font('Arial', 'B', 14)
    pdf.cell(0, 10, 'Channel-wise Results', 0, 1)
    pdf.set_font('Arial', '', 10)

    pdf.cell(60, 8, 'Channel', 1, 0, 'C')
    pdf.cell(40, 8, 'HFO Count', 1, 0, 'C')
    pdf.cell(40, 8, 'HFO Rate', 1, 0, 'C')
    pdf.cell(50, 8, 'Avg Duration (ms)', 1, 1, 'C')

    for ch_info in hfo_results['channels'][:20]:
        channel_name = f"Channel {ch_info['channel_index']}"
        hfo_count = ch_info['hfo_count']
        hfo_rate = ch_info['hfo_rate']

        if hfo_count > 0 and ch_info['events']:
            avg_duration = sum(e['duration_ms']
                               for e in ch_info['events']) / len(ch_info['events'])
        else:
            avg_duration = 0

        pdf.cell(60, 8, channel_name, 1, 0)
        pdf.cell(40, 8, str(hfo_count), 1, 0, 'C')
        pdf.cell(40, 8, f"{hfo_rate:.3f}", 1, 0, 'C')
        pdf.cell(50, 8, f"{avg_duration:.1f}", 1, 1, 'C')

    if len(hfo_results['channels']) > 20:
        pdf.cell(
            0, 8,
            f"... and {len(hfo_results['channels']) - 20} more channels",
            0, 1, 'C'
        )

    for i, viz_key in enumerate(visualization_keys[:5]):
        pdf.add_page()
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, f'Visualization {i + 1}', 0, 1)

        try:
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
                s3.download_file(S3_BUCKET, viz_key, tmp.name)

                img = Image.open(tmp.name)
                img_width, img_height = img.size

                max_width = 190
                max_height = 200

                aspect_ratio = img_width / img_height

                if img_width > max_width:
                    new_width = max_width
                    new_height = new_width / aspect_ratio
                else:
                    new_width = img_width * 0.264583
                    new_height = img_height * 0.264583

                if new_height > max_height:
                    new_height = max_height
                    new_width = new_height * aspect_ratio

                x = (210 - new_width) / 2

                pdf.image(tmp.name, x=x, y=pdf.get_y() + 5, w=new_width)
                os.unlink(tmp.name)

        except Exception as e:
            print(f"Error adding visualization to PDF: {e}")
            pdf.cell(0, 10, f"Error loading visualization: {str(e)}", 0, 1)

    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
        pdf.output(tmp.name)

        s3_key = f"users/{user_id}/temp/{analysis_id}/report.pdf"
        s3.upload_file(tmp.name, S3_BUCKET, s3_key)
        os.unlink(tmp.name)

    return s3_key
