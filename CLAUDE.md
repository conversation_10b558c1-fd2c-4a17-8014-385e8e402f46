# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

Always use AWS Profile --aws-profile kirtan-default

### Frontend (biormika-new-frontend)
- `npm run dev` - Start development server with Vite
- `npm run build` - Build for production (TypeScript compilation + Vite build)
- `npm run lint` - Run ESLint for code quality checks
- `npm run preview` - Preview production build locally
- `npm run test:api` - Run API integration tests
- `node deploy.js` - Deploy to S3 with automatic bucket configuration

### Backend (Biormika-AWS-Backend)
- `npm run offline` - Run serverless offline (API: http://localhost:3001, WebSocket: ws://localhost:3002)
- `npm run deploy:dev` - Build Docker image, push to ECR, and deploy to AWS development
- `npm run deploy:prod` - Build Docker image, push to ECR, and deploy to AWS production
- `npm run build:image` - Build Docker container image for Lambda
- `npm run push:ecr` - Push container image to Amazon ECR
- `python -m pytest tests/` - Run backend tests
- `pip install -r requirements.txt` - Install Python dependencies locally
- `npm install` - Install Node.js dependencies
- `./scripts/run-tests.sh` - Run full test suite with coverage

## Project Structure

```
/Users/<USER>/Work/biormika/main/
├── .claude/                    # Claude AI assistant configuration
│   ├── commands/              # Custom Claude commands
│   └── settings.local.json    # Local Claude settings
├── Biormika-AWS-Backend/      # AWS serverless backend
├── biormika-new-frontend/     # React frontend application  
├── biormika-old-backend/      # Legacy desktop application (reference)
├── tasks/                     # Project task definitions
└── CLAUDE.md                  # This documentation file
```

## Architecture Overview

This is a serverless EEG/HFO (High-Frequency Oscillation) detection platform built with AWS services and React. The system was migrated from a desktop application (`biormika-old-backend/`) to a serverless architecture while preserving the core algorithms and business logic.

### Backend Architecture (AWS Serverless)

The backend is a serverless architecture using AWS Lambda functions orchestrated by the Serverless Framework:

- **AWS Lambda**: All API endpoints and processing tasks run as Lambda functions (deployed as container images)
- **ECR**: Container registry for Lambda function images (supports up to 10GB for scientific libraries)
- **API Gateway**: REST API endpoints and WebSocket connections for real-time updates
- **S3**: Storage for uploaded EDF files and analysis results
- **DynamoDB**: Metadata storage for users, files, and analysis records
- **Cognito**: User authentication and JWT token management
- **Step Functions**: Orchestration of the multi-step analysis workflow
- **CloudWatch**: Centralized logging and monitoring

**Container-Based Deployment**: The backend uses Docker container images instead of ZIP packages to support large scientific libraries (numpy, scipy, matplotlib) that exceed Lambda's 250MB limit. See `docs/deployment-container.md` for details.

#### Core Processing Flow
1. User uploads EDF file → S3 (via presigned URL)
2. File validation Lambda triggered
3. Analysis initiated via Step Functions
4. HFO detection algorithm processes EDF data
5. Results stored in S3, metadata in DynamoDB
6. WebSocket updates sent during processing
7. PDF report generated with visualizations

#### Key Backend Modules
- `analysis/core/hfo_analysis.py` - Core HFO detection algorithm
- `analysis/edf/reader.py` - EDF file format parsing
- `auth/` - JWT authentication and authorization
- `files/` - File upload and validation handlers
- `websocket/` - Real-time progress updates
- `visualization/` - PDF report and plot generation
- `storage/` - S3 and DynamoDB utilities

### Legacy Backend (biormika-old-backend) - Reference Implementation

The original desktop application contains the **proven algorithms** that the AWS backend must replicate. This is the working reference implementation:

#### Core Algorithm Files
- `hfo_analysis.py` - **Master HFO detection algorithm** (Threshold Scheme 10)
- `calculate_HFO_characteristics.py` - HFO spectral analysis and validation
- `pyedfreader.py` - EDF file binary reading and signal extraction
- `plotAndSaveHfoV2.py` - Visualization and statistical reporting
- `find_blanks.py` - Data discontinuity detection and removal

#### Critical Business Logic (Must Be Preserved)

**HFO Detection Pipeline (Threshold Scheme 10):**
1. **Data Preprocessing**: Load EDF → Apply montage → Filter (notch 60Hz + bandpass)
2. **Energy Signal**: Calculate Hilbert transform → Generate RMS2 energy signal
3. **Statistical Thresholding**: `threshold = meanHilbert + (multiplier * stdHilbert)`
4. **HFO Detection**: Find segments where energy > threshold for ≥10ms duration
5. **Multi-stage Validation**:
   - Peak count validation (≥6 peaks above amplitude1, ≥3 above amplitude2)
   - Frequency validation (≥70 Hz required for HFO classification)
   - Power ratio validation (HFO range vs conventional range)
6. **Connectivity Analysis**: Spatial-temporal synchronization between channels
7. **Report Generation**: Calculate rates, densities, and connectivity metrics

**Key Parameters (Default Values):**
- Amplitude thresholds: 5 STD (amplitude1), 3 STD (amplitude2)
- Peak requirements: 6 peaks (threshold1), 3 peaks (threshold2)
- Minimum duration: 10ms
- Frequency threshold: 70 Hz minimum
- Temporal sync window: 10ms
- Spatial sync window: 10ms

**Critical Processing Steps:**
1. **Filtering Pipeline**: 6th-order Butterworth bandpass (50-300 Hz typical)
2. **Blank Detection**: Remove data discontinuities and artifacts
3. **Energy Statistics**: Calculate mean and STD of Hilbert-transformed signal
4. **HFO Characterization**: FFT analysis with Hamming window and zero-padding
5. **Validation Chain**: Energy → Duration → Peaks → Frequency → Power ratio

**AWS Implementation Note**: The serverless backend should replicate this exact algorithm flow, parameters, and validation logic to ensure consistent results with the proven desktop version.

### Frontend Architecture

The frontend is a modern React SPA with enterprise-grade state management:

- **React 19 + TypeScript** - Type-safe component development
- **Vite** - Fast build tooling and HMR
- **Redux Toolkit** - Centralized state management with persistence
- **React Router v7** - Declarative routing with auth guards
- **Ant Design + Tailwind CSS** - Hybrid styling approach
- **Axios** - HTTP client with interceptors

#### State Management Philosophy
- All persistent/shared state managed through Redux
- File uploads, auth state, and analysis settings in Redux slices
- Local component state only for UI-specific concerns
- Redux Persist for localStorage persistence

#### Routing Architecture
- Declarative route configuration in `src/router/routes.ts`
- Automatic auth guard application based on route flags
- Protected routes redirect to login with return URL preservation
- Guest-only routes (login/signup) redirect authenticated users

### API Integration

#### Authentication Flow
1. Login/signup returns JWT tokens (access + refresh)
2. Tokens stored in Redux and persisted to localStorage
3. Axios interceptor adds Authorization header
4. 401 responses trigger token refresh automatically
5. Failed refresh redirects to login

#### File Upload Flow
1. Initiate upload to get presigned S3 URL
2. Direct upload to S3 from browser
3. Validate file format on backend
4. Real-time status updates via Redux

#### Analysis Configuration
```typescript
interface AnalysisSettings {
  thresholdSettings: {
    amplitude1: number  // Default: 5
    amplitude2: number  // Default: 3
    peaks1: number      // Default: 6
    peaks2: number      // Default: 3
    duration: number    // Default: 10ms
  }
  frequencyFilterSettings: {
    lowCutoffFilter: number   // 80Hz for ripples
    highCutoffFilter: number  // 250Hz for ripples, 500Hz for fast ripples
  }
  montageType: 'bipolar' | 'average' | 'referential'
}
```

### Key Development Patterns

#### Component Structure
- Functional components with TypeScript interfaces
- Consistent props pattern with proper typing
- Error boundaries wrap major sections
- Custom hooks for complex logic

#### State Access Pattern
```typescript
// Always use typed hooks
const dispatch = useAppDispatch()
const { data, isLoading } = useAppSelector(state => state.slice)
```

#### Error Handling
- Global error boundaries for crash protection
- Toast notifications for user feedback
- Structured error responses from API
- Graceful degradation on failures

#### Security Considerations
- JWT tokens with short expiration
- Row-level security in DynamoDB
- Presigned URLs expire after upload
- API request validation
- CORS configuration per environment

### Testing Approach

#### Backend Testing
- Python pytest for Lambda function tests
- Mock AWS services with moto library
- Integration tests for Step Functions
- Unit tests for HFO algorithm

#### Frontend Testing
- Check with team for preferred testing framework
- Component testing likely with React Testing Library
- E2E tests potentially with Cypress/Playwright

### Deployment

#### Backend Deployment
```bash
# Deploy to development
serverless deploy --stage dev

# Deploy to production
serverless deploy --stage prod
```

#### Frontend Deployment
```bash
# Build for production
npm run build

# Deploy script handles S3 upload and CloudFront invalidation
node deploy.js
```

### Environment Configuration

#### Backend (.env.dev)
- AWS credentials and region
- Cognito pool IDs
- S3 bucket names
- DynamoDB table names
- WebSocket endpoint

#### Frontend (.env)
- API endpoint URLs
- WebSocket URL
- Cognito client ID
- Environment name

### Common Development Tasks

#### Adding a New API Endpoint
1. Define Lambda handler in appropriate module
2. Add function configuration to serverless.yml
3. Update API documentation
4. Add TypeScript types in frontend
5. Create Redux actions if stateful
6. Update Axios service layer

#### Modifying HFO Algorithm
1. Core logic in `analysis/core/hfo_analysis.py`
2. Update threshold validation ranges
3. Test with sample EDF files
4. Update visualization if needed
5. Document parameter changes

#### Adding New File Validations
1. Update `files/validators.py`
2. Add specific format checks
3. Update error messages
4. Test with edge cases
5. Update frontend validation

### Troubleshooting

#### Common Issues
- **CORS errors**: Check serverless.yml CORS configuration
- **WebSocket disconnects**: Verify WebSocket URL and auth
- **File upload fails**: Check S3 bucket permissions and CORS
- **Analysis stuck**: Check Step Functions execution in AWS console
- **Auth errors**: Verify Cognito configuration and token expiry

#### Debugging Tools
- CloudWatch Logs for Lambda execution
- Step Functions visual workflow
- DynamoDB table explorer
- S3 bucket browser
- Redux DevTools for state inspection

### Performance Considerations

#### Backend Optimization
- Lambda cold start mitigation with provisioned concurrency
- DynamoDB on-demand scaling
- S3 transfer acceleration for large files
- Step Functions express workflows for speed
- Lambda layers for shared dependencies
- Connection pooling for database operations
- Multipart upload support for large files
- Batch operations for DynamoDB queries

#### Frontend Optimization
- Code splitting with React.lazy
- Redux state normalization
- Virtual scrolling for large file lists
- Image optimization in PDF reports
- Memoization for expensive computations
- Network status monitoring with offline detection
- Automatic request queuing during token refresh

### Local Development Setup

#### Prerequisites
- Docker Desktop installed and running (for container image building)
- AWS CLI configured with development credentials
- Python 3.9+ and Node.js 18+

#### Backend Local Development

1. **Run Serverless Offline**:
   ```bash
   cd Biormika-AWS-Backend
   npm run offline
   ```
   - API available at http://localhost:3001
   - WebSocket at ws://localhost:3002
   - Uses AWS development environment services (S3, DynamoDB, Cognito)
   - Requires active internet connection and AWS credentials

#### Frontend Local Development

1. **Configure Environment**:
   ```bash
   cd biormika-new-frontend
   cp .env.example .env
   # Edit .env to point to local backend
   ```

2. **Start Development Server**:
   ```bash
   npm install
   npm run dev
   ```
   - Frontend at http://localhost:5173
   - HMR enabled for instant updates

### Authentication System

#### Social Authentication
The platform supports multiple authentication methods:

1. **Email/Password Authentication**
   - Standard Cognito user pool authentication
   - Email verification required
   - Password reset flow available

2. **Google OAuth Integration**
   - `/auth/google` endpoint
   - Automatic Cognito user creation
   - Profile data synchronization

3. **Facebook OAuth Integration**
   - `/auth/facebook` endpoint
   - Seamless user provisioning
   - Social profile linking

#### Token Management
- **Access Token**: Short-lived JWT (1 hour)
- **Refresh Token**: Long-lived token (30 days)
- **Automatic Refresh**: Axios interceptor handles token refresh
- **Request Queue**: Pending requests queued during refresh
- **Secure Storage**: Tokens persisted in Redux with encryption

### API Testing Infrastructure

#### Integration Testing
```bash
cd biormika-new-frontend
npm run test:api
```

The test suite covers:
- All REST endpoints functionality
- CORS configuration verification
- WebSocket connection testing
- Authentication flows
- File upload/download workflows
- Error handling scenarios

#### Test Configuration
- Tests use `.env.test` configuration
- Automated setup and teardown
- Parallel test execution support
- Detailed logging for debugging

### Advanced Features

#### Error Handling Framework
- **Custom Error Classes**: Typed errors for different scenarios
  - `ValidationError`: Input validation failures
  - `AuthenticationError`: Auth-related issues
  - `NotFoundError`: Resource not found
  - `ConflictError`: Business logic conflicts
- **Error Decorator**: `@error_handler` for consistent responses
- **Structured Logging**: JSON-formatted error logs
- **User-Friendly Messages**: Translated error codes

#### Logging and Monitoring
- **Structured JSON Logs**: Consistent format across services
- **Request Tracking**: Correlation IDs for request tracing
- **Performance Metrics**: Built-in timing measurements
- **CloudWatch Integration**: Automatic log aggregation
- **Audit Trail**: Security-sensitive operation logging

#### WebSocket Features
- **Connection Management**: Automatic cleanup and reconnection
- **Channel Subscriptions**: Topic-based message routing
- **Broadcast System**: Multi-client updates
- **Keep-Alive**: Ping/pong for connection health
- **Progress Tracking**: Real-time analysis updates

#### File Management
- **Presigned URLs**: Secure S3 uploads/downloads
- **Metadata Tracking**: File properties in DynamoDB
- **Validation Pipeline**: Format and size checks
- **Virus Scanning**: Optional ClamAV integration
- **Automatic Cleanup**: TTL-based file expiration

### Development Workflow

#### Feature Development Process
1. Create feature branch from `develop`
2. Update tests for new functionality
3. Implement feature with error handling
4. Run linting and type checking
5. Execute test suite
6. Update documentation
7. Create pull request

#### Code Quality Tools
- **Backend**:
  - `black`: Python code formatting
  - `pylint`: Python linting
  - `mypy`: Type checking
  - `pytest`: Testing framework
  
- **Frontend**:
  - `prettier`: Code formatting
  - `eslint`: JavaScript/TypeScript linting
  - `typescript`: Type checking
  - Integration test suite

#### CI/CD Pipeline (if configured)
1. Automated testing on push
2. Code quality checks
3. Security vulnerability scanning
4. Deployment to staging
5. Smoke tests execution
6. Production deployment approval
7. Post-deployment verification