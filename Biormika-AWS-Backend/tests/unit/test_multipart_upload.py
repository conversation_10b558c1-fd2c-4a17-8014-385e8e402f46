import pytest
import boto3
import os
from moto import mock_aws
from unittest.mock import patch

# Set environment variable before importing the module
os.environ['S3_BUCKET'] = 'test-biormika-files'

from storage.multipart_upload import (
    initiate_multipart_upload,
    generate_part_upload_urls,
    complete_multipart_upload
)


@pytest.fixture
def mock_s3_client():
    with mock_aws():
        client = boto3.client('s3', region_name='us-east-1')
        bucket_name = 'test-biormika-files'
        client.create_bucket(Bucket=bucket_name)
        yield client, bucket_name


class TestMultipartUpload:

    @patch.dict(os.environ, {'S3_BUCKET': 'test-biormika-files'})
    def test_initiate_multipart_upload(self, mock_s3_client):
        s3_client, bucket_name = mock_s3_client

        with patch('storage.multipart_upload.s3', s3_client):
            result = initiate_multipart_upload(
                file_name='test.edf',
                user_id='test-user-123',
                content_type='application/octet-stream'
            )

            assert 'uploadId' in result
            assert 's3Key' in result
            assert 'bucket' in result
            assert result['bucket'] == bucket_name
            assert 'test-user-123' in result['s3Key']
            assert 'multipart_' in result['s3Key']
            assert result['s3Key'].endswith('.edf')

    @patch.dict(os.environ, {'S3_BUCKET': 'test-biormika-files'})
    def test_generate_part_upload_urls_with_content_type(self, mock_s3_client):
        s3_client, bucket_name = mock_s3_client

        # First initiate a multipart upload
        with patch('storage.multipart_upload.s3', s3_client):
            init_result = initiate_multipart_upload(
                file_name='test.edf',
                user_id='test-user-123',
                content_type='application/octet-stream'
            )

            # Generate presigned URLs for parts
            presigned_urls = generate_part_upload_urls(
                s3_key=init_result['s3Key'],
                upload_id=init_result['uploadId'],
                parts=3,
                content_type='application/octet-stream'
            )

            assert len(presigned_urls) == 3

            for i, url_info in enumerate(presigned_urls):
                assert 'partNumber' in url_info
                assert 'url' in url_info
                assert url_info['partNumber'] == i + 1

                # Verify the URL contains the correct parameters
                url = url_info['url']
                assert bucket_name in url
                assert init_result['s3Key'] in url
                assert f'partNumber={i + 1}' in url
                assert f'uploadId={init_result["uploadId"]}' in url

                # Verify Content-Type is included in the signature
                # The URL should be properly signed for PUT requests with Content-Type
                assert 'X-Amz-Signature' in url

    @patch.dict(os.environ, {'S3_BUCKET': 'test-biormika-files'})
    def test_generate_part_upload_urls_default_content_type(self, mock_s3_client):
        s3_client, bucket_name = mock_s3_client

        with patch('storage.multipart_upload.s3', s3_client):
            init_result = initiate_multipart_upload(
                file_name='test.edf',
                user_id='test-user-123'
            )

            # Generate presigned URLs without specifying content_type (should use default)
            presigned_urls = generate_part_upload_urls(
                s3_key=init_result['s3Key'],
                upload_id=init_result['uploadId'],
                parts=2
            )

            assert len(presigned_urls) == 2

            for url_info in presigned_urls:
                assert 'partNumber' in url_info
                assert 'url' in url_info
                assert 'X-Amz-Signature' in url_info['url']

    @patch.dict(os.environ, {'S3_BUCKET': 'test-biormika-files'})
    def test_complete_multipart_upload(self, mock_s3_client):
        s3_client, bucket_name = mock_s3_client

        with patch('storage.multipart_upload.s3', s3_client):
            # Initiate multipart upload
            init_result = initiate_multipart_upload(
                file_name='test.edf',
                user_id='test-user-123'
            )

            # Mock parts (in real scenario these would come from actual uploads)
            parts = [
                {'PartNumber': 1, 'ETag': '"etag1"'},
                {'PartNumber': 2, 'ETag': '"etag2"'}
            ]

            # Complete the upload
            result = complete_multipart_upload(
                s3_key=init_result['s3Key'],
                upload_id=init_result['uploadId'],
                parts=parts
            )

            assert 'location' in result
            assert bucket_name in result['location']
            assert init_result['s3Key'] in result['location']

    def test_presigned_url_signature_consistency(self):
        """Test that presigned URLs generated with same parameters produce consistent signatures"""
        with mock_aws():
            client = boto3.client('s3', region_name='us-east-1')
            bucket_name = 'test-bucket'
            client.create_bucket(Bucket=bucket_name)

            with patch('storage.multipart_upload.s3', client), \
                    patch.dict(os.environ, {'S3_BUCKET': bucket_name}):

                # Generate URLs twice with same parameters
                s3_key = 'test/file.edf'
                upload_id = 'test-upload-id'

                urls1 = generate_part_upload_urls(
                    s3_key=s3_key,
                    upload_id=upload_id,
                    parts=1,
                    content_type='application/octet-stream'
                )

                urls2 = generate_part_upload_urls(
                    s3_key=s3_key,
                    upload_id=upload_id,
                    parts=1,
                    content_type='application/octet-stream'
                )

                # URLs should be different due to timestamps, but structure should be consistent
                assert len(urls1) == len(urls2) == 1
                assert urls1[0]['partNumber'] == urls2[0]['partNumber']

                # Both should contain the same base parameters
                url1 = urls1[0]['url']
                url2 = urls2[0]['url']

                assert 'partNumber=1' in url1 and 'partNumber=1' in url2
                assert upload_id in url1 and upload_id in url2
                assert s3_key in url1 and s3_key in url2
