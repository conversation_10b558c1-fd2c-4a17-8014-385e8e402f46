import { But<PERSON> } from 'antd'
import type { ReactNode } from 'react'
import { FaGoogle, FaFacebook } from 'react-icons/fa'
import { brandColors } from '../constants/colors'

interface SocialButtonProps {
  provider: 'google' | 'facebook'
  children: ReactNode
  onClick?: () => void
  className?: string
}

export function SocialButton({ provider, children, onClick, className = '' }: SocialButtonProps) {
  const Icon = provider === 'google' ? FaGoogle : FaFacebook

  return (
    <Button
      style={{ backgroundColor: brandColors.socialButtonBackground }}
      className={`
        w-full h-12 border-gray-200 hover:border-gray-300 
        rounded-lg font-medium text-gray-700 
        flex items-center justify-center gap-3
        ${className}
      `}
      icon={<Icon className="w-5 h-5" />}
      onClick={onClick}
    >
      {children}
    </Button>
  )
}
