export interface ThresholdSettings {
  amplitude1: number
  amplitude2: number
  peaks1: number
  peaks2: number
  duration: number
}

export interface SynchronizationSettings {
  temporalSync: number
  spatialSync: number
}

export interface MontageSelection {
  bipolar: boolean
  average: boolean
  referential: boolean
}

export interface FrequencyFilterSettings {
  lowCutoffFilter: number
  highCutoffFilter: number
}

export interface SegmentSelectionSettings {
  lowCutoffFilter: number
  highCutoffFilter: number
  entireFile: boolean
  startEndTimes: boolean
  startTimeDuration: boolean
  startDate?: string
  startTime?: string
  endDate?: string
  endTime?: string
  durationInSeconds?: number
}

export interface AnalysisSettings {
  thresholdSettings: ThresholdSettings
  synchronizationSettings: SynchronizationSettings
  montageSelection: MontageSelection
  frequencyFilterSettings: FrequencyFilterSettings
  segmentSelectionSettings: SegmentSelectionSettings
}

export interface AnalysisSettingsState {
  globalSettings: AnalysisSettings | null
  fileSpecificSettings: Record<string, AnalysisSettings>
  isConfigured: boolean
  lastConfigured?: Date
}

export interface AnalysisSettingsPayload {
  settings: AnalysisSettings
  fileId?: string
}