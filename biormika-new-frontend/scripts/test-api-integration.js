const axios = require('axios').default

// Test configuration
const API_BASE_URL = process.env.VITE_API_BASE_URL || 'http://localhost:3001'
const WEBSOCKET_URL = process.env.VITE_WEBSOCKET_URL || 'ws://localhost:3002'
const TEST_ORIGIN = 'http://localhost:3000'

// Test data
const testUser = {
  email: `test-${Date.now()}@example.com`,
  password: 'TestPassword123!',
  name: 'Test User'
}

let authToken = null
let refreshToken = null

// Helper function to format test results
function logTest(name, status, details) {
  const statusSymbol = status === 'PASS' ? '✅' : '❌'
  console.log(`${statusSymbol} ${name}`)
  if (details) {
    console.log(`   ${details}`)
  }
}

// Helper function to check CORS headers
function checkCorsHeaders(headers, testName) {
  const corsHeaders = {
    'access-control-allow-origin': headers['access-control-allow-origin'],
    'access-control-allow-credentials': headers['access-control-allow-credentials'],
  }
  
  const hasRequiredHeaders = corsHeaders['access-control-allow-origin'] !== undefined
  
  if (hasRequiredHeaders) {
    logTest(`${testName} - CORS Headers`, 'PASS', `Origin: ${corsHeaders['access-control-allow-origin']}`)
  } else {
    logTest(`${testName} - CORS Headers`, 'FAIL', 'Missing CORS headers')
  }
  
  return hasRequiredHeaders
}

// Quick connectivity test
async function testConnectivity() {
  console.log('\n🔌 Testing Basic Connectivity...\n')
  
  // Test if backend is running
  try {
    await axios.get(`${API_BASE_URL}/health`, {
      headers: { 'Origin': TEST_ORIGIN },
      timeout: 5000
    })
    logTest('Backend Health Check', 'PASS', 'Backend is running')
  } catch (error) {
    if (error.response) {
      logTest('Backend Health Check', 'PASS', 'Backend is running (health endpoint not found)')
    } else if (error.code === 'ECONNREFUSED') {
      logTest('Backend Health Check', 'FAIL', 'Backend is not running - start with: npm run offline')
      return false
    } else {
      logTest('Backend Health Check', 'FAIL', error.message)
      return false
    }
  }
  
  return true
}

// Test auth endpoints
async function testAuthEndpoints() {
  console.log('\n🔐 Testing Authentication Endpoints...\n')
  
  // Test login with test credentials
  try {
    const loginResponse = await axios.post(
      `${API_BASE_URL}/auth/login`,
      {
        email: '<EMAIL>',
        password: 'password123'
      },
      {
        headers: {
          'Origin': TEST_ORIGIN,
          'Content-Type': 'application/json'
        }
      }
    )
    
    if (loginResponse.data.success) {
      logTest('POST /auth/login', 'PASS', 'Login endpoint working')
      checkCorsHeaders(loginResponse.headers, 'Login')
      authToken = loginResponse.data.data.token
    } else {
      logTest('POST /auth/login', 'FAIL', loginResponse.data.message)
    }
  } catch (error) {
    if (error.response) {
      // Check CORS headers even on error
      checkCorsHeaders(error.response.headers, 'Login (Error Response)')
      logTest('POST /auth/login', 'PASS', `Endpoint accessible (${error.response.status})`)
    } else {
      logTest('POST /auth/login', 'FAIL', error.message)
    }
  }
  
  // Test signup
  try {
    const signupResponse = await axios.post(
      `${API_BASE_URL}/auth/signup`,
      testUser,
      {
        headers: {
          'Origin': TEST_ORIGIN,
          'Content-Type': 'application/json'
        }
      }
    )
    
    if (signupResponse.data.success) {
      logTest('POST /auth/signup', 'PASS', 'Signup endpoint working')
      checkCorsHeaders(signupResponse.headers, 'Signup')
    }
  } catch (error) {
    if (error.response) {
      checkCorsHeaders(error.response.headers, 'Signup (Error Response)')
      logTest('POST /auth/signup', 'PASS', `Endpoint accessible (${error.response.status})`)
    } else {
      logTest('POST /auth/signup', 'FAIL', error.message)
    }
  }
}

// Test preflight
async function testPreflightRequest() {
  console.log('\n✈️  Testing CORS Preflight...\n')
  
  try {
    const response = await axios({
      method: 'OPTIONS',
      url: `${API_BASE_URL}/auth/login`,
      headers: {
        'Origin': TEST_ORIGIN,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type,Authorization'
      }
    })
    
    const hasAllHeaders = 
      response.headers['access-control-allow-origin'] &&
      response.headers['access-control-allow-methods'] &&
      response.headers['access-control-allow-headers']
    
    if (hasAllHeaders) {
      logTest('Preflight Request', 'PASS', 'All CORS headers present')
      console.log('   Allowed Origin:', response.headers['access-control-allow-origin'])
      console.log('   Allowed Methods:', response.headers['access-control-allow-methods'])
      console.log('   Allowed Headers:', response.headers['access-control-allow-headers'])
    } else {
      logTest('Preflight Request', 'FAIL', 'Missing some CORS headers')
    }
  } catch (error) {
    logTest('Preflight Request', 'FAIL', error.message)
  }
}

// Generate recommendations
function generateRecommendations() {
  console.log('\n💡 Recommendations:\n')
  console.log('1. Development Setup:')
  console.log('   - Ensure backend is running: cd Biormika-AWS-Backend && npm run offline')
  console.log('   - Frontend dev server: cd biormika-new-frontend && npm run dev')
  console.log('   - Vite proxy is configured to forward requests to localhost:3001')
  console.log('')
  console.log('2. If CORS errors persist:')
  console.log('   - Check that serverless offline is running on port 3001')
  console.log('   - Verify environment variables in .env file')
  console.log('   - Try accessing API directly at http://localhost:3001')
  console.log('')
  console.log('3. For production:')
  console.log('   - Update Access-Control-Allow-Origin from "*" to specific domains')
  console.log('   - Ensure HTTPS is enabled for all endpoints')
  console.log('   - Configure CloudFront (if used) to forward necessary headers')
}

// Main test runner
async function runTests() {
  console.log('🚀 API Integration Test')
  console.log('═'.repeat(50))
  console.log(`API URL: ${API_BASE_URL}`)
  console.log(`Test Origin: ${TEST_ORIGIN}`)
  console.log('═'.repeat(50))
  
  const isBackendRunning = await testConnectivity()
  
  if (isBackendRunning) {
    await testAuthEndpoints()
    await testPreflightRequest()
  }
  
  generateRecommendations()
}

// Run tests
runTests().catch(console.error)