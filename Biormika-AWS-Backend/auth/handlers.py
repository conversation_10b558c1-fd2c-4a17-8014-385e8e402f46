import json
import os
from auth.responses import success_response, error_response
from auth.validators import validate_email, validate_name, sanitize_input
from auth.password_policy import validate_password, get_password_requirements
from auth.cognito_handlers import create_user, authenticate_user, get_user_info, refresh_user_token

if not os.environ.get('COGNITO_USER_POOL_ID') or not os.environ.get('COGNITO_CLIENT_ID'):
    raise EnvironmentError(
        "Missing required environment variables: COGNITO_USER_POOL_ID, COGNITO_CLIENT_ID")


def login(event, context):
    try:
        body = json.loads(event['body'])
        email = sanitize_input(body.get('email', ''))
        password = body.get('password', '')

        if not email or not password:
            return error_response('Email and password are required', 400)

        is_valid_email, validated_email = validate_email(email)
        if not is_valid_email:
            return error_response(validated_email, 400)

        auth_result, error = authenticate_user(validated_email, password)
        if error:
            return error_response(error[0], error[1])

        user_attributes, error = get_user_info(validated_email)
        if error:
            return error_response(error[0], error[1])

        user_data = {
            'id': user_attributes.get('sub'),
            'email': user_attributes.get('email'),
            'name': user_attributes.get('name', ''),
            'emailVerified': user_attributes.get('email_verified', 'false') == 'true',
            'role': 'user',  # Default role
            'permissions': []  # Default permissions
        }

        response_data = {
            'user': user_data,
            'token': auth_result['AuthenticationResult']['AccessToken'],
            'refreshToken': auth_result['AuthenticationResult']['RefreshToken']
        }

        return success_response(response_data, 'Login successful')

    except json.JSONDecodeError:
        return error_response('Invalid request body', 400)
    except Exception as e:
        print(f"Login error: {str(e)}")
        return error_response('Internal server error', 500)


def signup(event, context):
    try:
        body = json.loads(event['body'])
        email = sanitize_input(body.get('email', ''))
        password = body.get('password', '')
        name = sanitize_input(body.get('name', ''))

        if not email or not password or not name:
            return error_response('Email, password, and name are required', 400)

        is_valid_email, validated_email = validate_email(email)
        if not is_valid_email:
            return error_response(validated_email, 400)

        is_valid_name, validated_name = validate_name(name)
        if not is_valid_name:
            return error_response(validated_name, 400)

        is_valid_password, password_error = validate_password(password)
        if not is_valid_password:
            return error_response(password_error, 400, 'INVALID_PASSWORD')

        user_response, error = create_user(
            validated_email, password, validated_name)
        if error:
            return error_response(error[0], error[1])

        auth_result, error = authenticate_user(validated_email, password)
        if error:
            return error_response('User created but login failed. Please try logging in.', 201)

        user_attributes = {attr['Name']: attr['Value']
                           for attr in user_response['User']['Attributes']}

        user_data = {
            'id': user_attributes.get('sub'),
            'email': user_attributes.get('email'),
            'name': user_attributes.get('name', ''),
            'emailVerified': user_attributes.get('email_verified', 'false') == 'true',
            'role': 'user',  # Default role
            'permissions': []  # Default permissions
        }

        response_data = {
            'user': user_data,
            'token': auth_result['AuthenticationResult']['AccessToken'],
            'refreshToken': auth_result['AuthenticationResult']['RefreshToken'],
            'passwordRequirements': get_password_requirements()
        }

        return success_response(response_data, 'User created successfully', 201)

    except json.JSONDecodeError:
        return error_response('Invalid request body', 400)
    except Exception as e:
        print(f"Signup error: {str(e)}")
        return error_response('Internal server error', 500)


def refresh_token(event, context):
    try:
        body = json.loads(event['body'])
        refresh_token = body.get('refreshToken')

        if not refresh_token:
            return error_response('Refresh token is required', 400)

        auth_result, error = refresh_user_token(refresh_token)
        if error:
            return error_response(error[0], error[1])

        token_data = {
            'token': auth_result['AuthenticationResult']['AccessToken'],
            'refreshToken': refresh_token,  # Return the same refresh token
            'expiresIn': auth_result['AuthenticationResult'].get('ExpiresIn', 3600)
        }

        return success_response(token_data, 'Token refreshed successfully')

    except json.JSONDecodeError:
        return error_response('Invalid request body', 400)
    except Exception as e:
        print(f"Refresh token error: {str(e)}")
        return error_response('Internal server error', 500)


def validate_token(event, context):
    """
    Validate an access token and return user information
    """
    try:
        # Get the Authorization header
        authorization_header = event.get(
            'headers', {}).get('Authorization', '')

        if not authorization_header:
            return error_response('Authorization header is required', 401)

        # Extract token from Bearer header
        if not authorization_header.startswith('Bearer '):
            return error_response('Invalid authorization header format', 401)

        token = authorization_header.replace('Bearer ', '')

        # Validate the token using the existing decorator logic
        from auth.decorators import verify_cognito_token
        user_info, error = verify_cognito_token(token)

        if error:
            return error_response(error, 401)

        # Return user information
        return success_response({
            'user': user_info,
            'valid': True
        }, 'Token is valid')

    except Exception as e:
        print(f"Token validation error: {str(e)}")
        return error_response('Token validation failed', 500)
