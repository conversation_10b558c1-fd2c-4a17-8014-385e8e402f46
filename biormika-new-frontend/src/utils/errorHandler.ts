import { showToast } from './toast'
import { store } from '../store'
import { logout } from '../store/slices/authSlice'
import { HTTP_STATUS } from '../constants/api'

export interface ApiError {
  status?: number
  code?: string
  message: string
  details?: unknown
  timestamp?: string
}

export class ApiErrorHandler {
  private static instance: ApiErrorHandler
  private errorHandlers: Map<number | string, (error: ApiError) => void> = new Map()
  
  private constructor() {
    this.setupDefaultHandlers()
  }
  
  static getInstance(): ApiErrorHandler {
    if (!ApiErrorHandler.instance) {
      ApiErrorHandler.instance = new ApiErrorHandler()
    }
    return ApiErrorHandler.instance
  }
  
  private setupDefaultHandlers(): void {
    this.errorHandlers.set(HTTP_STATUS.UNAUTHORIZED, () => {
      store.dispatch(logout())
      showToast.error('Session expired. Please login again.')
      window.location.href = '/login'
    })
    
    this.errorHandlers.set(HTTP_STATUS.FORBIDDEN, () => {
      showToast.error('You do not have permission to perform this action')
    })
    
    this.errorHandlers.set(HTTP_STATUS.NOT_FOUND, (error) => {
      showToast.error(error.message || 'Resource not found')
    })
    
    this.errorHandlers.set(HTTP_STATUS.CONFLICT, (error) => {
      showToast.error(error.message || 'Resource conflict occurred')
    })
    
    this.errorHandlers.set(HTTP_STATUS.BAD_REQUEST, (error) => {
      showToast.error(error.message || 'Invalid request')
    })
    
    this.errorHandlers.set(HTTP_STATUS.INTERNAL_SERVER_ERROR, () => {
      showToast.error('Server error occurred. Please try again later.')
    })
    
    this.errorHandlers.set(HTTP_STATUS.SERVICE_UNAVAILABLE, () => {
      showToast.error('Service temporarily unavailable. Please try again later.')
    })
    
    this.errorHandlers.set('NETWORK_ERROR', () => {
      showToast.error('Network error. Please check your connection.')
    })
    
    this.errorHandlers.set('TIMEOUT', () => {
      showToast.error('Request timeout. Please try again.')
    })
  }
  
  registerHandler(statusOrCode: number | string, handler: (error: ApiError) => void): void {
    this.errorHandlers.set(statusOrCode, handler)
  }
  
  handleError(error: unknown): void {
    const apiError = this.parseError(error)
    
    const handler = this.errorHandlers.get(apiError.status || apiError.code || 'DEFAULT')
    
    if (handler) {
      handler(apiError)
    } else {
      showToast.error(apiError.message)
    }
    
    if (import.meta.env.VITE_ENABLE_DEBUG_MODE === 'true') {
      console.error('API Error:', apiError)
    }
  }
  
  private parseError(error: unknown): ApiError {
    const err = error as {code?: string; response?: {status?: number; data?: {code?: string; message?: string; error?: string; details?: unknown; timestamp?: string}}; message?: string}
    if (err.code === 'ECONNABORTED') {
      return {
        code: 'TIMEOUT',
        message: 'Request timeout',
        timestamp: new Date().toISOString()
      }
    }
    
    if (err.code === 'ERR_NETWORK' || !err.response) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network error occurred',
        timestamp: new Date().toISOString()
      }
    }
    
    if (err.response) {
      const { status, data } = err.response
      
      return {
        status,
        code: data?.code,
        message: data?.message || data?.error || (status ? this.getDefaultMessage(status) : 'An error occurred'),
        details: data?.details,
        timestamp: data?.timestamp || new Date().toISOString()
      }
    }
    
    return {
      message: err.message || 'An unexpected error occurred',
      timestamp: new Date().toISOString()
    }
  }
  
  private getDefaultMessage(status: number): string {
    switch (status) {
      case HTTP_STATUS.BAD_REQUEST:
        return 'Invalid request'
      case HTTP_STATUS.UNAUTHORIZED:
        return 'Authentication required'
      case HTTP_STATUS.FORBIDDEN:
        return 'Access denied'
      case HTTP_STATUS.NOT_FOUND:
        return 'Resource not found'
      case HTTP_STATUS.CONFLICT:
        return 'Resource conflict'
      case HTTP_STATUS.INTERNAL_SERVER_ERROR:
        return 'Server error'
      case HTTP_STATUS.SERVICE_UNAVAILABLE:
        return 'Service unavailable'
      default:
        return 'An error occurred'
    }
  }
}

export const apiErrorHandler = ApiErrorHandler.getInstance()

export function handleApiError(error: unknown): void {
  apiErrorHandler.handleError(error)
}

export function createErrorFromResponse(response: unknown): Error {
  const apiError = apiErrorHandler['parseError'](response)
  const error = new Error(apiError.message)
  Object.assign(error, apiError)
  return error
}