import { LogOut, Home } from 'lucide-react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAppDispatch } from '../store'
import { logout } from '../store/slices/authSlice'
import { brandColors } from '../constants/colors'

export function UserProfileHeader() {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useAppDispatch()

  const handleLogout = () => {
    dispatch(logout())
    navigate('/login')
  }

  const handleHelp = () => {
    window.open('https://docs.biormika.com', '_blank')
  }

  const handleSettings = () => {
    navigate('/configuration')
  }

  const handleDashboard = () => {
    navigate('/')
  }

  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  const getNavButtonStyle = (path: string) => {
    const active = isActive(path)
    return {
      color: active ? brandColors.primary : brandColors.text.secondary,
      backgroundColor: active ? `${brandColors.primary}10` : 'transparent',
      borderRadius: '6px',
      padding: '8px 12px',
      transition: 'all 0.2s ease-in-out',
    }
  }

  return (
    <header 
      className="w-full px-6 py-4 border-b"
      style={{ 
        backgroundColor: brandColors.background.white,
        borderColor: brandColors.border.light 
      }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <h1 
            className="text-xl font-bold"
            style={{ color: brandColors.text.primary }}
          >
            Biormika
          </h1>
        </div>
        
        <div className="flex items-center gap-4">
          <button 
            onClick={handleDashboard}
            className="flex items-center gap-2 font-medium transition-colors"
            style={getNavButtonStyle('/')}
            onMouseEnter={(e) => {
              if (!isActive('/')) {
                e.currentTarget.style.color = brandColors.text.primary
                e.currentTarget.style.backgroundColor = brandColors.background.light
              }
            }}
            onMouseLeave={(e) => {
              if (!isActive('/')) {
                e.currentTarget.style.color = brandColors.text.secondary
                e.currentTarget.style.backgroundColor = 'transparent'
              }
            }}
          >
            <Home className="w-4 h-4" />
            Dashboard
          </button>

          <button 
            onClick={handleSettings}
            className="font-medium transition-colors"
            style={getNavButtonStyle('/configuration')}
            onMouseEnter={(e) => {
              if (!isActive('/configuration')) {
                e.currentTarget.style.color = brandColors.text.primary
                e.currentTarget.style.backgroundColor = brandColors.background.light
              }
            }}
            onMouseLeave={(e) => {
              if (!isActive('/configuration')) {
                e.currentTarget.style.color = brandColors.text.secondary
                e.currentTarget.style.backgroundColor = 'transparent'
              }
            }}
          >
            Settings
          </button>
          
          <button 
            onClick={handleHelp}
            className="font-medium transition-colors"
            style={{
              color: brandColors.text.secondary,
              padding: '8px 12px',
              borderRadius: '6px',
              transition: 'all 0.2s ease-in-out',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = brandColors.text.primary
              e.currentTarget.style.backgroundColor = brandColors.background.light
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = brandColors.text.secondary
              e.currentTarget.style.backgroundColor = 'transparent'
            }}
          >
            Help
          </button>
          
          <div className="flex items-center gap-3">
            <span 
              className="font-medium"
              style={{ color: brandColors.text.primary }}
            >
              John Doe
            </span>
            <button 
              onClick={handleLogout}
              className="p-1 transition-colors"
              style={{ color: brandColors.text.secondary }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = brandColors.text.primary
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = brandColors.text.secondary
              }}
              title="Logout"
            >
              <LogOut size={20} />
            </button>
          </div>
        </div>
      </div>
    </header>
  )
}