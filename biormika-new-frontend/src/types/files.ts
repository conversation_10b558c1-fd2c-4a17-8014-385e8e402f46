export interface FileUpload {
  id: string
  name: string
  size: number
  type: string
  status: FileStatus
  uploadProgress: number
  errorMessage?: string
  uploadedAt: string
  serverFileId?: string
}

export const FileStatus = {
  PENDING: 'pending',
  UPLOADING: 'uploading', 
  VALIDATING: 'validating',
  VALID: 'valid',
  ERROR: 'error',
  COMPLETED: 'completed'
} as const

export type FileStatus = typeof FileStatus[keyof typeof FileStatus]

export interface FilesState {
  files: FileUpload[]
  isUploading: boolean
  totalFiles: number
  completedFiles: number
}

export interface AddFilesPayload {
  files: Array<{
    id: string
    file: File
  }>
}

export interface UpdateFileStatusPayload {
  id: string
  status: FileStatus
  errorMessage?: string
  serverFileId?: string
}

export interface UpdateFileProgressPayload {
  id: string
  progress: number
}