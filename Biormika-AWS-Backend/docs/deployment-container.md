# Container-Based Lambda Deployment

This document explains the container-based deployment approach for the Biormika AWS Backend, implemented to handle large scientific Python libraries that exceed Lambda's traditional deployment size limits.

## Why Container Deployment?

The scientific computing libraries required for HFO analysis (numpy, scipy, matplotlib) exceed AWS Lambda's 250MB unzipped deployment package limit. Container images support up to 10GB, providing ample space for all dependencies.

## Architecture Overview

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Docker    │────▶│     ECR     │────▶│   Lambda    │
│   Image     │     │ Repository  │     │  Functions  │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Deployment Process

### 1. Build and Push Container Image

```bash
# For development environment
npm run deploy:dev

# For production environment  
npm run deploy:prod
```

This command will:
1. Build a Docker image with all Python dependencies
2. Create an ECR repository (if it doesn't exist)
3. Push the image to ECR
4. Deploy Lambda functions using the container image

### 2. Manual Build Steps

If you need to build and push manually:

```bash
# Build Docker image
npm run build:image

# Or run the script directly
./scripts/build-and-push.sh [stage] [region] [aws-profile]
```

## Container Structure

### Dockerfile

The `Dockerfile` configures the Lambda runtime environment:

```dockerfile
FROM public.ecr.aws/lambda/python:3.9  # Official AWS Lambda base image
RUN yum install -y gcc gcc-c++ libgfortran  # Required for scipy
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . ${LAMBDA_TASK_ROOT}/
```

### Function Configuration

Each Lambda function in `serverless.yml` references the container image:

```yaml
functions:
  detectHfo:
    image:
      name: biormika-lambda      # ECR image reference
      command:
        - analysis/tasks.detect_hfo  # Handler override
    timeout: 900
    memorySize: 3008
```

## Requirements Management

### Python Dependencies

All Python dependencies are listed in `requirements.txt`:
- numpy>=1.26.0,<2.0.0
- scipy>=1.11.0,<2.0.0  
- matplotlib==3.8.4
- Other scientific and utility libraries

### System Dependencies

System-level dependencies are installed in the Dockerfile:
- gcc, gcc-c++: Required for compiling C extensions
- libgfortran: Required for scipy's Fortran components

## Troubleshooting

### Common Issues

1. **ECR Login Failures**
   ```bash
   # Manually authenticate Docker with ECR
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin [account-id].dkr.ecr.us-east-1.amazonaws.com
   ```

2. **Build Failures**
   - Ensure Docker Desktop is running
   - Check that AWS credentials are configured
   - Verify the AWS profile has ECR permissions

3. **Deployment Timeouts**
   - Container images are large (~1GB)
   - First deployment may take 10-15 minutes
   - Subsequent deployments use cached layers

### Performance Considerations

- **Cold Starts**: Container-based Lambdas have longer cold starts (3-5 seconds)
- **Image Size**: Keep unnecessary files out using `.dockerignore`
- **Layer Caching**: Docker caches unchanged layers for faster rebuilds

## Local Development

For local testing without containers:

```bash
# Run serverless offline (uses local Python environment)
npm run offline
```

Note: Ensure your local environment has all required dependencies installed.

## Migration from ZIP Deployment

The migration involved:
1. Removing `pythonRequirements` plugin configuration
2. Adding ECR image configuration to provider section
3. Converting function handlers to image-based configuration
4. Creating Dockerfile and build scripts

## Security Considerations

- ECR repositories are private by default
- Images are scanned for vulnerabilities by ECR
- IAM roles control access to ECR repositories
- Container runs with Lambda's security context