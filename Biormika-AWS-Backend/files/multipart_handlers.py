import json
import uuid
from auth.decorators import auth_required
from storage.multipart_upload import (
    initiate_multipart_upload,
    generate_part_upload_urls,
    complete_multipart_upload,
    abort_multipart_upload
)
from storage.metadata import save_file_metadata, update_file_status


def create_response(status_code, body):
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
        },
        'body': json.dumps(body)
    }


@auth_required
def init_multipart(event, context):
    try:
        body = json.loads(event['body'])
        file_name = body.get('fileName')
        file_size = body.get('fileSize')
        content_type = body.get('contentType', 'application/octet-stream')
        
        if not file_name or not file_size:
            return create_response(400, {
                'success': False,
                'message': 'File name and size are required',
                'statusCode': 400
            })
        
        if file_size > 524288000:  # 500MB limit
            return create_response(400, {
                'success': False,
                'message': 'File size exceeds 500MB limit',
                'statusCode': 400
            })
        
        if not file_name.lower().endswith('.edf'):
            return create_response(400, {
                'success': False,
                'message': 'Only EDF files are supported',
                'statusCode': 400
            })
        
        user = event.get('user', {})
        user_id = user.get('id')
        
        try:
            file_id = str(uuid.uuid4())
            
            upload_data = initiate_multipart_upload(
                file_name=file_name,
                user_id=user_id,
                content_type=content_type
            )
            
            save_file_metadata({
                'fileId': file_id,
                'fileName': file_name,
                'fileSize': file_size,
                's3Key': upload_data['s3Key'],
                'contentType': content_type,
                'status': 'uploading',
                'userId': user_id,
                'uploadId': upload_data['uploadId'],
                'uploadType': 'multipart'
            })
            
            return create_response(200, {
                'success': True,
                'data': {
                    'fileId': file_id,
                    'uploadId': upload_data['uploadId'],
                    's3Key': upload_data['s3Key'],
                    'bucket': upload_data['bucket']
                },
                'message': 'Multipart upload initialized successfully',
                'statusCode': 200
            })
            
        except Exception as e:
            print(f"Error initializing multipart upload: {e}")
            return create_response(500, {
                'success': False,
                'message': 'Failed to initialize multipart upload',
                'statusCode': 500
            })
            
    except json.JSONDecodeError:
        return create_response(400, {
            'success': False,
            'message': 'Invalid request body',
            'statusCode': 400
        })
    except Exception as e:
        print(f"Init multipart error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })


@auth_required
def get_upload_urls(event, context):
    try:
        body = json.loads(event['body'])
        upload_id = body.get('uploadId')
        s3_key = body.get('s3Key')
        parts = body.get('parts')
        
        if not upload_id or not s3_key or not parts:
            return create_response(400, {
                'success': False,
                'message': 'Upload ID, S3 key, and parts count are required',
                'statusCode': 400
            })
        
        if parts < 1 or parts > 10000:
            return create_response(400, {
                'success': False,
                'message': 'Parts must be between 1 and 10000',
                'statusCode': 400
            })
        
        print(f"Generating presigned URLs for multipart upload: uploadId={upload_id}, s3Key={s3_key}, parts={parts}")
        
        try:
            presigned_urls = generate_part_upload_urls(
                s3_key=s3_key,
                upload_id=upload_id,
                parts=parts,
                content_type='application/octet-stream'
            )
            
            print(f"Successfully generated {len(presigned_urls)} presigned URLs")
            
            return create_response(200, {
                'success': True,
                'data': {
                    'urls': presigned_urls
                },
                'message': 'Presigned URLs generated successfully',
                'statusCode': 200
            })
            
        except Exception as e:
            print(f"Error generating upload URLs: {e}")
            return create_response(500, {
                'success': False,
                'message': 'Failed to generate upload URLs',
                'statusCode': 500
            })
            
    except json.JSONDecodeError:
        return create_response(400, {
            'success': False,
            'message': 'Invalid request body',
            'statusCode': 400
        })
    except Exception as e:
        print(f"Get upload URLs error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })


@auth_required
def complete_upload(event, context):
    try:
        body = json.loads(event['body'])
        file_id = body.get('fileId')
        upload_id = body.get('uploadId')
        s3_key = body.get('s3Key')
        parts = body.get('parts')
        
        if not file_id or not upload_id or not s3_key or not parts:
            return create_response(400, {
                'success': False,
                'message': 'File ID, upload ID, S3 key, and parts are required',
                'statusCode': 400
            })
        
        if not isinstance(parts, list) or len(parts) == 0:
            return create_response(400, {
                'success': False,
                'message': 'Parts must be a non-empty array',
                'statusCode': 400
            })
        
        user = event.get('user', {})
        user_id = user.get('id')
        
        print(f"Completing multipart upload: fileId={file_id}, uploadId={upload_id}, s3Key={s3_key}, parts_count={len(parts)}")
        
        try:
            result = complete_multipart_upload(
                s3_key=s3_key,
                upload_id=upload_id,
                parts=parts
            )
            
            print(f"Multipart upload completed successfully: location={result.get('location')}")
            
            update_file_status(file_id, user_id, 'pending', {
                'uploadCompleted': True,
                'multipartResult': result
            })
            
            return create_response(200, {
                'success': True,
                'data': {
                    'fileId': file_id,
                    'location': result.get('location'),
                    'eTag': result.get('eTag')
                },
                'message': 'Multipart upload completed successfully',
                'statusCode': 200
            })
            
        except Exception as e:
            print(f"Error completing multipart upload: {e}")
            update_file_status(file_id, user_id, 'error', {
                'errorMessage': f'Failed to complete multipart upload: {str(e)}'
            })
            return create_response(500, {
                'success': False,
                'message': 'Failed to complete multipart upload',
                'statusCode': 500
            })
            
    except json.JSONDecodeError:
        return create_response(400, {
            'success': False,
            'message': 'Invalid request body',
            'statusCode': 400
        })
    except Exception as e:
        print(f"Complete upload error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })


@auth_required
def abort_upload(event, context):
    try:
        body = json.loads(event['body'])
        file_id = body.get('fileId')
        upload_id = body.get('uploadId')
        s3_key = body.get('s3Key')
        
        if not upload_id or not s3_key:
            return create_response(400, {
                'success': False,
                'message': 'Upload ID and S3 key are required',
                'statusCode': 400
            })
        
        user = event.get('user', {})
        user_id = user.get('id')
        
        try:
            success = abort_multipart_upload(
                s3_key=s3_key,
                upload_id=upload_id
            )
            
            if file_id:
                update_file_status(file_id, user_id, 'cancelled', {
                    'cancelledAt': True,
                    'uploadId': upload_id
                })
            
            return create_response(200, {
                'success': True,
                'data': {
                    'aborted': success
                },
                'message': 'Multipart upload aborted successfully',
                'statusCode': 200
            })
            
        except Exception as e:
            print(f"Error aborting multipart upload: {e}")
            return create_response(500, {
                'success': False,
                'message': 'Failed to abort multipart upload',
                'statusCode': 500
            })
            
    except json.JSONDecodeError:
        return create_response(400, {
            'success': False,
            'message': 'Invalid request body',
            'statusCode': 400
        })
    except Exception as e:
        print(f"Abort upload error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })