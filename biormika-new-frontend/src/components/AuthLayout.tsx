import type { ReactNode } from 'react'
import { WaveBackground } from './WaveBackground'
import { TopBar } from './TopBar'

interface AuthLayoutProps {
  children: ReactNode
  showTopBar?: boolean
}

export function AuthLayout({ children, showTopBar = true }: AuthLayoutProps) {
  return (
    <div className="min-h-screen relative">
      <WaveBackground />
      
      {showTopBar && <TopBar />}
      
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
        {children}
      </div>

      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 text-center text-gray-500 text-xs z-20">
        <span>Privacy Policy | Terms & Conditions | © All Rights Reserved 2025</span>
      </div>
    </div>
  )
}