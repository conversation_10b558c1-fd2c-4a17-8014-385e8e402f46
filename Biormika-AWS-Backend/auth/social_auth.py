import json
import os
import boto3
import requests
from auth.responses import success_response, error_response
from auth.cognito_handlers import authenticate_user, get_user_info

cognito = boto3.client('cognito-idp')
USER_POOL_ID = os.environ.get('COGNITO_USER_POOL_ID')
CLIENT_ID = os.environ.get('COGNITO_CLIENT_ID')

def validate_google_token(id_token):
    try:
        response = requests.post(
            'https://oauth2.googleapis.com/tokeninfo',
            data={'id_token': id_token}
        )
        
        if response.status_code != 200:
            return None, "Invalid Google token"
        
        return response.json(), None
    except Exception as e:
        print(f"Google token validation error: {e}")
        return None, "Failed to validate Google token"

def validate_facebook_token(access_token):
    try:
        response = requests.get(
            'https://graph.facebook.com/me',
            params={
                'fields': 'id,name,email,picture',
                'access_token': access_token
            }
        )
        
        if response.status_code != 200:
            return None, "Invalid Facebook token"
        
        data = response.json()
        if 'error' in data:
            return None, data['error'].get('message', 'Facebook authentication failed')
        
        return data, None
    except Exception as e:
        print(f"Facebook token validation error: {e}")
        return None, "Failed to validate Facebook token"

def create_or_update_social_user(email, name, picture=None, provider=None):
    try:
        user_response = cognito.admin_get_user(
            UserPoolId=USER_POOL_ID,
            Username=email
        )
        
        user_attributes = {attr['Name']: attr['Value'] 
                         for attr in user_response['UserAttributes']}
        
        if picture and picture != user_attributes.get('picture'):
            cognito.admin_update_user_attributes(
                UserPoolId=USER_POOL_ID,
                Username=email,
                UserAttributes=[
                    {'Name': 'picture', 'Value': picture}
                ]
            )
            user_attributes['picture'] = picture
        
        return user_attributes, None
        
    except cognito.exceptions.UserNotFoundException:
        try:
            import secrets
            temp_password = secrets.token_urlsafe(32)
            
            response = cognito.admin_create_user(
                UserPoolId=USER_POOL_ID,
                Username=email,
                UserAttributes=[
                    {'Name': 'email', 'Value': email},
                    {'Name': 'name', 'Value': name},
                    {'Name': 'email_verified', 'Value': 'true'},
                    {'Name': 'picture', 'Value': picture or ''}
                ],
                TemporaryPassword=temp_password,
                MessageAction='SUPPRESS'
            )
            
            cognito.admin_set_user_password(
                UserPoolId=USER_POOL_ID,
                Username=email,
                Password=temp_password,
                Permanent=True
            )
            
            user_response = cognito.admin_get_user(
                UserPoolId=USER_POOL_ID,
                Username=email
            )
            
            user_attributes = {attr['Name']: attr['Value'] 
                             for attr in user_response['UserAttributes']}
            
            return user_attributes, None
            
        except Exception as e:
            print(f"Error creating social user: {e}")
            return None, "Failed to create user account"

def google_auth(event, context):
    try:
        body = json.loads(event['body'])
        id_token = body.get('idToken')
        
        if not id_token:
            return error_response('Google ID token is required', 400)
        
        google_data, error = validate_google_token(id_token)
        if error:
            return error_response(error, 401)
        
        email = google_data.get('email')
        name = google_data.get('name')
        picture = google_data.get('picture')
        
        if not email:
            return error_response('Email not provided by Google', 400)
        
        user_attributes, error = create_or_update_social_user(
            email, name, picture, 'google'
        )
        
        if error:
            return error_response(error, 500)
        
        try:
            import secrets
            temp_password = secrets.token_urlsafe(32)
            
            auth_result = cognito.admin_initiate_auth(
                UserPoolId=USER_POOL_ID,
                ClientId=CLIENT_ID,
                AuthFlow='ADMIN_NO_SRP_AUTH',
                AuthParameters={
                    'USERNAME': email,
                    'PASSWORD': temp_password
                }
            )
            
            token_data = {
                'accessToken': auth_result['AuthenticationResult']['AccessToken'],
                'idToken': auth_result['AuthenticationResult']['IdToken'],
                'refreshToken': auth_result['AuthenticationResult']['RefreshToken'],
                'expiresIn': auth_result['AuthenticationResult']['ExpiresIn']
            }
        except:
            return error_response('Authentication successful but token generation failed. Please login with email.', 200)
        
        user_data = {
            'id': user_attributes.get('sub'),
            'email': user_attributes.get('email'),
            'name': user_attributes.get('name', ''),
            'avatar': user_attributes.get('picture', ''),
            'emailVerified': True
        }
        
        return success_response({
            'user': user_data,
            'tokens': token_data
        }, 'Google authentication successful')
        
    except json.JSONDecodeError:
        return error_response('Invalid request body', 400)
    except Exception as e:
        print(f"Google auth error: {str(e)}")
        return error_response('Internal server error', 500)

def facebook_auth(event, context):
    try:
        body = json.loads(event['body'])
        access_token = body.get('accessToken')
        
        if not access_token:
            return error_response('Facebook access token is required', 400)
        
        fb_data, error = validate_facebook_token(access_token)
        if error:
            return error_response(error, 401)
        
        email = fb_data.get('email')
        name = fb_data.get('name')
        picture = fb_data.get('picture', {}).get('data', {}).get('url')
        
        if not email:
            return error_response('Email permission is required', 400)
        
        user_attributes, error = create_or_update_social_user(
            email, name, picture, 'facebook'
        )
        
        if error:
            return error_response(error, 500)
        
        try:
            import secrets
            temp_password = secrets.token_urlsafe(32)
            
            auth_result = cognito.admin_initiate_auth(
                UserPoolId=USER_POOL_ID,
                ClientId=CLIENT_ID,
                AuthFlow='ADMIN_NO_SRP_AUTH',
                AuthParameters={
                    'USERNAME': email,
                    'PASSWORD': temp_password
                }
            )
            
            token_data = {
                'accessToken': auth_result['AuthenticationResult']['AccessToken'],
                'idToken': auth_result['AuthenticationResult']['IdToken'],
                'refreshToken': auth_result['AuthenticationResult']['RefreshToken'],
                'expiresIn': auth_result['AuthenticationResult']['ExpiresIn']
            }
        except:
            return error_response('Authentication successful but token generation failed. Please login with email.', 200)
        
        user_data = {
            'id': user_attributes.get('sub'),
            'email': user_attributes.get('email'),
            'name': user_attributes.get('name', ''),
            'avatar': user_attributes.get('picture', ''),
            'emailVerified': True
        }
        
        return success_response({
            'user': user_data,
            'tokens': token_data
        }, 'Facebook authentication successful')
        
    except json.JSONDecodeError:
        return error_response('Invalid request body', 400)
    except Exception as e:
        print(f"Facebook auth error: {str(e)}")
        return error_response('Internal server error', 500)