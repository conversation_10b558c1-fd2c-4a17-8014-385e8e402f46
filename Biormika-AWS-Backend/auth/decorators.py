import json
import functools
import os
import boto3
from botocore.exceptions import ClientError
from auth.responses import error_response

cognito = boto3.client('cognito-idp')
USER_POOL_ID = os.environ.get('COGNITO_USER_POOL_ID')

if not USER_POOL_ID:
    raise EnvironmentError("Missing required environment variable: COGNITO_USER_POOL_ID")

def verify_cognito_token(token):
    try:
        response = cognito.get_user(AccessToken=token)
        
        user_attributes = {attr['Name']: attr['Value'] 
                         for attr in response['UserAttributes']}
        
        return {
            'id': user_attributes.get('sub'),
            'email': user_attributes.get('email'),
            'name': user_attributes.get('name', ''),
            'emailVerified': user_attributes.get('email_verified', 'false') == 'true',
            'username': response['Username']
        }, None
    except cognito.exceptions.NotAuthorizedException:
        return None, "Invalid or expired token"
    except cognito.exceptions.TokenMismatchException:
        return None, "Token mismatch"
    except ClientError as e:
        print(f"Cognito verify token error: {e}")
        return None, "Token verification failed"

def auth_required(handler):
    @functools.wraps(handler)
    def wrapper(event, context):
        authorization_header = event.get('headers', {}).get('Authorization', '')
        
        if not authorization_header:
            return error_response('Authorization header is required', 401)
        
        try:
            token = authorization_header.replace('Bearer ', '')
            
            user_info, error = verify_cognito_token(token)
            if error:
                return error_response(error, 401)
            
            event['user'] = user_info
            
            return handler(event, context)
            
        except Exception as e:
            print(f"Auth error: {str(e)}")
            return error_response('Authentication error', 500)
    
    return wrapper

def get_user_from_event(event):
    return event.get('user', {})