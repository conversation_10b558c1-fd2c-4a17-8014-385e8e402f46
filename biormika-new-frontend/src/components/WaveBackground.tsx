interface WaveBackgroundProps {
  variant?: 'light' | 'dark'
}

export function WaveBackground({ variant = 'light' }: WaveBackgroundProps) {
  const isDark = variant === 'dark'

  return (
    <div className="absolute inset-0 overflow-hidden">
      <div
        className={`absolute inset-0 ${
          isDark
            ? 'bg-gradient-to-br from-brand-request-demo-bg via-slate-900 to-slate-800'
            : 'bg-gradient-to-br from-brand-page-background via-orange-50 to-orange-100'
        }`}
      />
    </div>
  )
}